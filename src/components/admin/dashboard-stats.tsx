'use client';

import { TrendingUp, TrendingDown, DollarSign, ShoppingCart, Users, Package } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency, formatNumber } from '@/lib/utils';

const stats = [
	{
		title: 'Tổng doanh thu',
		value: 125000000,
		change: 12.5,
		changeType: 'increase' as const,
		icon: DollarSign,
		color: 'text-green-600',
		bgColor: 'bg-green-100 dark:bg-green-900/20',
	},
	{
		title: 'Đơn hàng',
		value: 1234,
		change: 8.2,
		changeType: 'increase' as const,
		icon: ShoppingCart,
		color: 'text-blue-600',
		bgColor: 'bg-blue-100 dark:bg-blue-900/20',
	},
	{
		title: 'Khách hàng',
		value: 5678,
		change: -2.1,
		changeType: 'decrease' as const,
		icon: Users,
		color: 'text-purple-600',
		bgColor: 'bg-purple-100 dark:bg-purple-900/20',
	},
	{
		title: 'Sản phẩm',
		value: 892,
		change: 15.3,
		changeType: 'increase' as const,
		icon: Package,
		color: 'text-orange-600',
		bgColor: 'bg-orange-100 dark:bg-orange-900/20',
	},
];

export function DashboardStats() {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			{stats.map((stat) => (
				<Card key={stat.title} className="relative overflow-hidden">
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium text-muted-foreground">
							{stat.title}
						</CardTitle>
						<div className={`p-2 rounded-lg ${stat.bgColor}`}>
							<stat.icon className={`h-4 w-4 ${stat.color}`} />
						</div>
					</CardHeader>
					<CardContent>
						<div className="space-y-2">
							<div className="text-2xl font-bold">
								{stat.title === 'Tổng doanh thu' 
									? formatCurrency(stat.value)
									: formatNumber(stat.value)
								}
							</div>
							<div className="flex items-center space-x-1 text-sm">
								{stat.changeType === 'increase' ? (
									<TrendingUp className="h-4 w-4 text-green-600" />
								) : (
									<TrendingDown className="h-4 w-4 text-red-600" />
								)}
								<span className={
									stat.changeType === 'increase' 
										? 'text-green-600' 
										: 'text-red-600'
								}>
									{Math.abs(stat.change)}%
								</span>
								<span className="text-muted-foreground">so với tháng trước</span>
							</div>
						</div>
					</CardContent>
				</Card>
			))}
		</div>
	);
}
