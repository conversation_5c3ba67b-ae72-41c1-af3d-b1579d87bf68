import "@/app/globals.css";
import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { AuthProvider } from "@/providers/auth-provider";
import { CartProvider } from "@/contexts/cart-context";
import { SettingsProvider } from "@/contexts/SettingsContext";
import { Toaster } from "sonner";

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist",
  preload: true,
});

const geistMono = Geist_Mono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
  preload: true,
});

export const metadata: Metadata = {
  title: "NS Shop - Fashion Store",
  description: "Discover the latest fashion trends and styles at NS Shop",
  keywords: ["fashion", "clothing", "style", "shopping", "trends"],
  authors: [{ name: "NS Shop Team" }],
  openGraph: {
    title: "NS Shop - Fashion Store",
    description: "Discover the latest fashion trends and styles at NS Shop",
    type: "website",
    locale: "vi_VN",
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="vi" suppressHydrationWarning>
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body
        className={`${geist.variable} ${geistMono.variable} min-h-screen flex flex-col antialiased`}
        role="application"
        aria-label="NS Shop Fashion Store"
        suppressHydrationWarning
      >
        <AuthProvider>
          <SettingsProvider>
            <CartProvider>
              {children}
              <Toaster position="top-right" richColors />
            </CartProvider>
          </SettingsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
