import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { z } from 'zod';

const updateProductSchema = z.object({
	name: z.string().min(1).optional(),
	description: z.string().min(1).optional(),
	price: z.number().positive().optional(),
	salePrice: z.number().positive().optional(),
	images: z.array(z.string()).optional(),
	categoryId: z.string().optional(),
	stock: z.number().int().min(0).optional(),
	sku: z.string().optional(),
	featured: z.boolean().optional(),
	status: z.enum(['ACTIVE', 'INACTIVE', 'OUT_OF_STOCK']).optional(),
	tags: z.array(z.string()).optional(),
});

// GET /api/products/[id] - <PERSON><PERSON><PERSON> chi tiết sản phẩm
export async function GET(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		const product = await prisma.product.findUnique({
			where: { id: params.id },
			include: {
				category: {
					select: {
						id: true,
						name: true,
						slug: true,
					},
				},
				reviews: {
					include: {
						user: {
							select: {
								id: true,
								name: true,
								avatar: true,
							},
						},
					},
					orderBy: {
						createdAt: 'desc',
					},
				},
			},
		});

		if (!product) {
			return NextResponse.json(
				{ error: 'Không tìm thấy sản phẩm' },
				{ status: 404 }
			);
		}

		// Calculate average rating
		const avgRating = product.reviews.length > 0
			? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
			: 0;

		const productWithRating = {
			...product,
			avgRating: Math.round(avgRating * 10) / 10,
			reviewCount: product.reviews.length,
		};

		return NextResponse.json(productWithRating);
	} catch (error) {
		console.error('Get product error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy thông tin sản phẩm' },
			{ status: 500 }
		);
	}
}

// PUT /api/products/[id] - Cập nhật sản phẩm (Admin only)
export async function PUT(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		const session = await getServerSession(authOptions);

		if (!session || session.user.role !== 'ADMIN') {
			return NextResponse.json(
				{ error: 'Không có quyền truy cập' },
				{ status: 403 }
			);
		}

		const body = await request.json();
		const data = updateProductSchema.parse(body);

		// Check if product exists
		const existingProduct = await prisma.product.findUnique({
			where: { id: params.id },
		});

		if (!existingProduct) {
			return NextResponse.json(
				{ error: 'Không tìm thấy sản phẩm' },
				{ status: 404 }
			);
		}

		// Check SKU uniqueness if updating
		if (data.sku && data.sku !== existingProduct.sku) {
			const existingSku = await prisma.product.findUnique({
				where: { sku: data.sku },
			});

			if (existingSku) {
				return NextResponse.json(
					{ error: 'SKU đã tồn tại' },
					{ status: 400 }
				);
			}
		}

		// Update slug if name is changed
		let updateData = { ...data };
		if (data.name && data.name !== existingProduct.name) {
			const slug = data.name
				.toLowerCase()
				.replace(/[^a-z0-9\s-]/g, '')
				.replace(/\s+/g, '-')
				.replace(/-+/g, '-')
				.trim();

			let finalSlug = slug;
			let counter = 1;
			while (await prisma.product.findFirst({
				where: {
					slug: finalSlug,
					id: { not: params.id },
				},
			})) {
				finalSlug = `${slug}-${counter}`;
				counter++;
			}

			updateData.slug = finalSlug;
		}

		const product = await prisma.product.update({
			where: { id: params.id },
			data: updateData,
			include: {
				category: {
					select: {
						id: true,
						name: true,
						slug: true,
					},
				},
			},
		});

		return NextResponse.json({
			message: 'Cập nhật sản phẩm thành công',
			product,
		});
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{ error: error.errors[0].message },
				{ status: 400 }
			);
		}

		console.error('Update product error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi cập nhật sản phẩm' },
			{ status: 500 }
		);
	}
}

// DELETE /api/products/[id] - Xóa sản phẩm (Admin only)
export async function DELETE(
	request: NextRequest,
	{ params }: { params: { id: string } }
) {
	try {
		const session = await getServerSession(authOptions);

		if (!session || session.user.role !== 'ADMIN') {
			return NextResponse.json(
				{ error: 'Không có quyền truy cập' },
				{ status: 403 }
			);
		}

		// Check if product exists
		const existingProduct = await prisma.product.findUnique({
			where: { id: params.id },
		});

		if (!existingProduct) {
			return NextResponse.json(
				{ error: 'Không tìm thấy sản phẩm' },
				{ status: 404 }
			);
		}

		// Soft delete by setting status to INACTIVE
		await prisma.product.update({
			where: { id: params.id },
			data: { status: 'INACTIVE' },
		});

		return NextResponse.json({
			message: 'Xóa sản phẩm thành công',
		});
	} catch (error) {
		console.error('Delete product error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi xóa sản phẩm' },
			{ status: 500 }
		);
	}
}
