import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';

// GET /api/admin/products - <PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm cho admin
export async function GET(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session || session.user.role !== 'ADMIN') {
			return NextResponse.json(
				{ error: 'Không có quyền truy cập' },
				{ status: 403 }
			);
		}

		const { searchParams } = new URL(request.url);
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '20');
		const category = searchParams.get('category');
		const search = searchParams.get('search');
		const status = searchParams.get('status');
		const featured = searchParams.get('featured');

		const skip = (page - 1) * limit;

		// Build where clause
		const where: any = {};

		if (category) {
			where.categoryId = category;
		}

		if (search) {
			where.OR = [
				{ name: { contains: search, mode: 'insensitive' } },
				{ description: { contains: search, mode: 'insensitive' } },
				{ sku: { contains: search, mode: 'insensitive' } },
			];
		}

		if (status) {
			where.status = status;
		}

		if (featured === 'true') {
			where.featured = true;
		} else if (featured === 'false') {
			where.featured = false;
		}

		// Get products with pagination
		const [products, total] = await Promise.all([
			prisma.product.findMany({
				where,
				include: {
					category: {
						select: {
							id: true,
							name: true,
						},
					},
				},
				orderBy: {
					createdAt: 'desc',
				},
				skip,
				take: limit,
			}),
			prisma.product.count({ where }),
		]);

		return NextResponse.json({
			products,
			pagination: {
				page,
				limit,
				total,
				pages: Math.ceil(total / limit),
			},
		});
	} catch (error) {
		console.error('Get admin products error:', error);
		return NextResponse.json(
			{ error: 'Có lỗi xảy ra khi lấy danh sách sản phẩm' },
			{ status: 500 }
		);
	}
}
