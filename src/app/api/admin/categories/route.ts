import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';

// GET /api/admin/categories - <PERSON><PERSON><PERSON> danh sách categories cho admin
export async function GET(request: NextRequest) {
	try {
		const session = await getServerSession(authOptions);

		if (!session || session.user.role !== 'ADMIN') {
			return NextResponse.json(
				{ error: 'Không có quyền truy cập' },
				{ status: 403 }
			);
		}

		const categories = await prisma.category.findMany({
			include: {
				_count: {
					select: {
						products: true,
						children: true,
					},
				},
			},
			orderBy: [
				{ parentId: 'asc' }, // Parent categories first
				{ name: 'asc' },
			],
		});

		return NextResponse.json(categories);
	} catch (error) {
		console.error('Get admin categories error:', error);
		return NextResponse.json(
			{ error: '<PERSON><PERSON> lỗi xảy ra khi lấy danh sách danh mục' },
			{ status: 500 }
		);
	}
}
