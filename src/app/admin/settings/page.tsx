'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
	Settings, 
	Save, 
	Upload, 
	Globe, 
	Mail, 
	Phone,
	MapPin,
	CreditCard,
	Truck,
	Shield,
	Bell,
	Palette
} from 'lucide-react';
import { toast } from 'sonner';

interface SiteSettings {
	siteName: string;
	siteDescription: string;
	siteUrl: string;
	logo: string;
	favicon: string;
	contactEmail: string;
	contactPhone: string;
	address: string;
	socialMedia: {
		facebook: string;
		instagram: string;
		twitter: string;
	};
	paymentMethods: {
		cod: boolean;
		bankTransfer: boolean;
		creditCard: boolean;
	};
	shippingSettings: {
		freeShippingThreshold: number;
		shippingFee: number;
		estimatedDelivery: string;
	};
	emailSettings: {
		smtpHost: string;
		smtpPort: number;
		smtpUser: string;
		smtpPassword: string;
		fromEmail: string;
		fromName: string;
	};
	notifications: {
		orderNotifications: boolean;
		stockAlerts: boolean;
		customerNotifications: boolean;
	};
}

export default function AdminSettingsPage() {
	const [settings, setSettings] = useState<SiteSettings>({
		siteName: 'NS Shop',
		siteDescription: 'Cửa hàng thời trang trực tuyến',
		siteUrl: 'https://nsshop.com',
		logo: '',
		favicon: '',
		contactEmail: '<EMAIL>',
		contactPhone: '**********',
		address: '123 Đường ABC, Quận 1, TP.HCM',
		socialMedia: {
			facebook: '',
			instagram: '',
			twitter: '',
		},
		paymentMethods: {
			cod: true,
			bankTransfer: true,
			creditCard: false,
		},
		shippingSettings: {
			freeShippingThreshold: 500000,
			shippingFee: 30000,
			estimatedDelivery: '2-3 ngày',
		},
		emailSettings: {
			smtpHost: '',
			smtpPort: 587,
			smtpUser: '',
			smtpPassword: '',
			fromEmail: '<EMAIL>',
			fromName: 'NS Shop',
		},
		notifications: {
			orderNotifications: true,
			stockAlerts: true,
			customerNotifications: true,
		},
	});

	const [loading, setLoading] = useState(false);
	const [activeTab, setActiveTab] = useState('general');

	const handleSave = async () => {
		setLoading(true);
		try {
			// In a real app, you would save to database
			await new Promise(resolve => setTimeout(resolve, 1000));
			toast.success('Cài đặt đã được lưu thành công');
		} catch (error) {
			toast.error('Có lỗi xảy ra khi lưu cài đặt');
		} finally {
			setLoading(false);
		}
	};

	const handleInputChange = (section: string, field: string, value: any) => {
		setSettings(prev => ({
			...prev,
			[section]: {
				...prev[section as keyof SiteSettings],
				[field]: value,
			},
		}));
	};

	const handleDirectChange = (field: string, value: any) => {
		setSettings(prev => ({
			...prev,
			[field]: value,
		}));
	};

	const tabs = [
		{ id: 'general', label: 'Tổng quan', icon: Settings },
		{ id: 'contact', label: 'Liên hệ', icon: Phone },
		{ id: 'payment', label: 'Thanh toán', icon: CreditCard },
		{ id: 'shipping', label: 'Vận chuyển', icon: Truck },
		{ id: 'email', label: 'Email', icon: Mail },
		{ id: 'notifications', label: 'Thông báo', icon: Bell },
	];

	const renderTabContent = () => {
		switch (activeTab) {
			case 'general':
				return (
					<div className="space-y-6">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label className="block text-sm font-medium mb-2">
									Tên website
								</label>
								<input
									type="text"
									value={settings.siteName}
									onChange={(e) => handleDirectChange('siteName', e.target.value)}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								/>
							</div>
							<div>
								<label className="block text-sm font-medium mb-2">
									URL website
								</label>
								<input
									type="url"
									value={settings.siteUrl}
									onChange={(e) => handleDirectChange('siteUrl', e.target.value)}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								/>
							</div>
						</div>

						<div>
							<label className="block text-sm font-medium mb-2">
								Mô tả website
							</label>
							<textarea
								value={settings.siteDescription}
								onChange={(e) => handleDirectChange('siteDescription', e.target.value)}
								rows={3}
								className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							/>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label className="block text-sm font-medium mb-2">
									Logo
								</label>
								<div className="flex items-center gap-2">
									<input
										type="file"
										accept="image/*"
										className="hidden"
										id="logo-upload"
									/>
									<label
										htmlFor="logo-upload"
										className="flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
									>
										<Upload className="h-4 w-4" />
										Tải lên logo
									</label>
								</div>
							</div>
							<div>
								<label className="block text-sm font-medium mb-2">
									Favicon
								</label>
								<div className="flex items-center gap-2">
									<input
										type="file"
										accept="image/*"
										className="hidden"
										id="favicon-upload"
									/>
									<label
										htmlFor="favicon-upload"
										className="flex items-center gap-2 px-3 py-2 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
									>
										<Upload className="h-4 w-4" />
										Tải lên favicon
									</label>
								</div>
							</div>
						</div>
					</div>
				);

			case 'contact':
				return (
					<div className="space-y-6">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label className="block text-sm font-medium mb-2">
									Email liên hệ
								</label>
								<input
									type="email"
									value={settings.contactEmail}
									onChange={(e) => handleDirectChange('contactEmail', e.target.value)}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								/>
							</div>
							<div>
								<label className="block text-sm font-medium mb-2">
									Số điện thoại
								</label>
								<input
									type="tel"
									value={settings.contactPhone}
									onChange={(e) => handleDirectChange('contactPhone', e.target.value)}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								/>
							</div>
						</div>

						<div>
							<label className="block text-sm font-medium mb-2">
								Địa chỉ
							</label>
							<textarea
								value={settings.address}
								onChange={(e) => handleDirectChange('address', e.target.value)}
								rows={3}
								className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							/>
						</div>

						<div>
							<h3 className="text-lg font-medium mb-4">Mạng xã hội</h3>
							<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
								<div>
									<label className="block text-sm font-medium mb-2">
										Facebook
									</label>
									<input
										type="url"
										value={settings.socialMedia.facebook}
										onChange={(e) => handleInputChange('socialMedia', 'facebook', e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium mb-2">
										Instagram
									</label>
									<input
										type="url"
										value={settings.socialMedia.instagram}
										onChange={(e) => handleInputChange('socialMedia', 'instagram', e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium mb-2">
										Twitter
									</label>
									<input
										type="url"
										value={settings.socialMedia.twitter}
										onChange={(e) => handleInputChange('socialMedia', 'twitter', e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
									/>
								</div>
							</div>
						</div>
					</div>
				);

			case 'payment':
				return (
					<div className="space-y-6">
						<div>
							<h3 className="text-lg font-medium mb-4">Phương thức thanh toán</h3>
							<div className="space-y-4">
								<label className="flex items-center space-x-3">
									<input
										type="checkbox"
										checked={settings.paymentMethods.cod}
										onChange={(e) => handleInputChange('paymentMethods', 'cod', e.target.checked)}
										className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
									/>
									<span>Thanh toán khi nhận hàng (COD)</span>
								</label>
								<label className="flex items-center space-x-3">
									<input
										type="checkbox"
										checked={settings.paymentMethods.bankTransfer}
										onChange={(e) => handleInputChange('paymentMethods', 'bankTransfer', e.target.checked)}
										className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
									/>
									<span>Chuyển khoản ngân hàng</span>
								</label>
								<label className="flex items-center space-x-3">
									<input
										type="checkbox"
										checked={settings.paymentMethods.creditCard}
										onChange={(e) => handleInputChange('paymentMethods', 'creditCard', e.target.checked)}
										className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
									/>
									<span>Thẻ tín dụng</span>
								</label>
							</div>
						</div>
					</div>
				);

			case 'shipping':
				return (
					<div className="space-y-6">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label className="block text-sm font-medium mb-2">
									Miễn phí vận chuyển từ (VND)
								</label>
								<input
									type="number"
									value={settings.shippingSettings.freeShippingThreshold}
									onChange={(e) => handleInputChange('shippingSettings', 'freeShippingThreshold', parseInt(e.target.value))}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								/>
							</div>
							<div>
								<label className="block text-sm font-medium mb-2">
									Phí vận chuyển (VND)
								</label>
								<input
									type="number"
									value={settings.shippingSettings.shippingFee}
									onChange={(e) => handleInputChange('shippingSettings', 'shippingFee', parseInt(e.target.value))}
									className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
								/>
							</div>
						</div>

						<div>
							<label className="block text-sm font-medium mb-2">
								Thời gian giao hàng dự kiến
							</label>
							<input
								type="text"
								value={settings.shippingSettings.estimatedDelivery}
								onChange={(e) => handleInputChange('shippingSettings', 'estimatedDelivery', e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							/>
						</div>
					</div>
				);

			case 'notifications':
				return (
					<div className="space-y-6">
						<div>
							<h3 className="text-lg font-medium mb-4">Cài đặt thông báo</h3>
							<div className="space-y-4">
								<label className="flex items-center space-x-3">
									<input
										type="checkbox"
										checked={settings.notifications.orderNotifications}
										onChange={(e) => handleInputChange('notifications', 'orderNotifications', e.target.checked)}
										className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
									/>
									<span>Thông báo đơn hàng mới</span>
								</label>
								<label className="flex items-center space-x-3">
									<input
										type="checkbox"
										checked={settings.notifications.stockAlerts}
										onChange={(e) => handleInputChange('notifications', 'stockAlerts', e.target.checked)}
										className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
									/>
									<span>Cảnh báo hết hàng</span>
								</label>
								<label className="flex items-center space-x-3">
									<input
										type="checkbox"
										checked={settings.notifications.customerNotifications}
										onChange={(e) => handleInputChange('notifications', 'customerNotifications', e.target.checked)}
										className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
									/>
									<span>Thông báo khách hàng mới</span>
								</label>
							</div>
						</div>
					</div>
				);

			default:
				return <div>Tab content not found</div>;
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold">Cài đặt hệ thống</h1>
					<p className="text-muted-foreground">
						Quản lý cài đặt và cấu hình website
					</p>
				</div>
				<Button
					onClick={handleSave}
					disabled={loading}
					className="bg-pink-600 hover:bg-pink-700"
				>
					<Save className="h-4 w-4 mr-2" />
					{loading ? 'Đang lưu...' : 'Lưu cài đặt'}
				</Button>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
				{/* Sidebar */}
				<div className="lg:col-span-1">
					<Card>
						<CardContent className="p-0">
							<nav className="space-y-1">
								{tabs.map((tab) => {
									const Icon = tab.icon;
									return (
										<button
											key={tab.id}
											onClick={() => setActiveTab(tab.id)}
											className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
												activeTab === tab.id
													? 'bg-pink-50 text-pink-600 border-r-2 border-pink-600'
													: 'text-gray-600'
											}`}
										>
											<Icon className="h-4 w-4" />
											{tab.label}
										</button>
									);
								})}
							</nav>
						</CardContent>
					</Card>
				</div>

				{/* Content */}
				<div className="lg:col-span-3">
					<Card>
						<CardHeader>
							<CardTitle>
								{tabs.find(tab => tab.id === activeTab)?.label}
							</CardTitle>
						</CardHeader>
						<CardContent>
							{renderTabContent()}
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
