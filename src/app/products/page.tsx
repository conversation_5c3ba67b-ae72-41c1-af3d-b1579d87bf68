'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Header, Footer } from '@/components/layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Search, Filter, Grid, List, Star, Heart, ShoppingCart } from 'lucide-react';
import { toast } from 'sonner';

interface Product {
	id: string;
	name: string;
	price: number;
	salePrice?: number;
	images: string[];
	slug: string;
	avgRating: number;
	reviewCount: number;
	category: {
		id: string;
		name: string;
		slug: string;
	};
}

interface Category {
	id: string;
	name: string;
	slug: string;
}

export default function ProductsPage() {
	const searchParams = useSearchParams();
	const [products, setProducts] = useState<Product[]>([]);
	const [categories, setCategories] = useState<Category[]>([]);
	const [loading, setLoading] = useState(true);
	const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
	const [filters, setFilters] = useState({
		search: searchParams.get('search') || '',
		category: searchParams.get('category') || '',
		sortBy: searchParams.get('sortBy') || 'createdAt',
		sortOrder: searchParams.get('sortOrder') || 'desc',
	});
	const [pagination, setPagination] = useState({
		page: 1,
		limit: 12,
		total: 0,
		pages: 0,
	});

	// Fetch products
	const fetchProducts = async () => {
		setLoading(true);
		try {
			const params = new URLSearchParams({
				page: pagination.page.toString(),
				limit: pagination.limit.toString(),
				...(filters.search && { search: filters.search }),
				...(filters.category && { category: filters.category }),
				sortBy: filters.sortBy,
				sortOrder: filters.sortOrder,
			});

			const response = await fetch(`/api/products?${params}`);
			const data = await response.json();

			if (response.ok) {
				setProducts(data.products);
				setPagination(data.pagination);
			} else {
				toast.error('Có lỗi xảy ra khi tải sản phẩm');
			}
		} catch (error) {
			toast.error('Có lỗi xảy ra khi tải sản phẩm');
		} finally {
			setLoading(false);
		}
	};

	// Fetch categories
	const fetchCategories = async () => {
		try {
			const response = await fetch('/api/categories');
			const data = await response.json();

			if (response.ok) {
				setCategories(data);
			}
		} catch (error) {
			console.error('Error fetching categories:', error);
		}
	};

	useEffect(() => {
		fetchCategories();
	}, []);

	useEffect(() => {
		fetchProducts();
	}, [filters, pagination.page]);

	const handleSearch = (e: React.FormEvent) => {
		e.preventDefault();
		setPagination({ ...pagination, page: 1 });
		fetchProducts();
	};

	const handleFilterChange = (key: string, value: string) => {
		setFilters({ ...filters, [key]: value });
		setPagination({ ...pagination, page: 1 });
	};

	const formatPrice = (price: number) => {
		return new Intl.NumberFormat('vi-VN', {
			style: 'currency',
			currency: 'VND',
		}).format(price);
	};

	const renderStars = (rating: number) => {
		return Array.from({ length: 5 }, (_, i) => (
			<Star
				key={i}
				className={`h-4 w-4 ${
					i < Math.floor(rating)
						? 'text-yellow-400 fill-current'
						: 'text-gray-300'
				}`}
			/>
		));
	};

	return (
		<div className="min-h-screen flex flex-col">
			<Header />

			<main className="flex-1 container mx-auto px-4 py-8">
				{/* Page Header */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold mb-2">Sản phẩm</h1>
					<p className="text-muted-foreground">
						Khám phá bộ sưu tập thời trang mới nhất
					</p>
				</div>

				{/* Search and Filters */}
				<div className="mb-8 space-y-4">
					{/* Search Bar */}
					<form onSubmit={handleSearch} className="flex gap-2">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
							<input
								type="text"
								placeholder="Tìm kiếm sản phẩm..."
								value={filters.search}
								onChange={(e) => setFilters({ ...filters, search: e.target.value })}
								className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							/>
						</div>
						<Button type="submit" className="bg-pink-600 hover:bg-pink-700">
							Tìm kiếm
						</Button>
					</form>

					{/* Filters and View Mode */}
					<div className="flex flex-wrap items-center justify-between gap-4">
						<div className="flex flex-wrap items-center gap-4">
							{/* Category Filter */}
							<select
								value={filters.category}
								onChange={(e) => handleFilterChange('category', e.target.value)}
								className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							>
								<option value="">Tất cả danh mục</option>
								{categories.map((category) => (
									<option key={category.id} value={category.id}>
										{category.name}
									</option>
								))}
							</select>

							{/* Sort Filter */}
							<select
								value={`${filters.sortBy}-${filters.sortOrder}`}
								onChange={(e) => {
									const [sortBy, sortOrder] = e.target.value.split('-');
									setFilters({ ...filters, sortBy, sortOrder });
								}}
								className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
							>
								<option value="createdAt-desc">Mới nhất</option>
								<option value="createdAt-asc">Cũ nhất</option>
								<option value="price-asc">Giá thấp đến cao</option>
								<option value="price-desc">Giá cao đến thấp</option>
								<option value="name-asc">Tên A-Z</option>
								<option value="name-desc">Tên Z-A</option>
							</select>
						</div>

						{/* View Mode Toggle */}
						<div className="flex items-center gap-2">
							<Button
								variant={viewMode === 'grid' ? 'default' : 'outline'}
								size="sm"
								onClick={() => setViewMode('grid')}
							>
								<Grid className="h-4 w-4" />
							</Button>
							<Button
								variant={viewMode === 'list' ? 'default' : 'outline'}
								size="sm"
								onClick={() => setViewMode('list')}
							>
								<List className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</div>

				{/* Products Grid/List */}
				{loading ? (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
						{Array.from({ length: 8 }, (_, i) => (
							<Card key={i} className="animate-pulse">
								<div className="aspect-square bg-gray-200 rounded-t-lg" />
								<CardContent className="p-4 space-y-2">
									<div className="h-4 bg-gray-200 rounded" />
									<div className="h-4 bg-gray-200 rounded w-2/3" />
									<div className="h-4 bg-gray-200 rounded w-1/2" />
								</CardContent>
							</Card>
						))}
					</div>
				) : products.length === 0 ? (
					<div className="text-center py-12">
						<p className="text-muted-foreground">Không tìm thấy sản phẩm nào</p>
					</div>
				) : (
					<div
						className={
							viewMode === 'grid'
								? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
								: 'space-y-4'
						}
					>
						{products.map((product) => (
							<Card
								key={product.id}
								className={`group hover:shadow-lg transition-shadow ${
									viewMode === 'list' ? 'flex' : ''
								}`}
							>
								<Link href={`/products/${product.slug}`} className="block">
									<div
										className={`relative overflow-hidden ${
											viewMode === 'list'
												? 'w-48 h-48 flex-shrink-0'
												: 'aspect-square'
										} rounded-t-lg`}
									>
										<Image
											src={product.images[0] || '/images/placeholder.jpg'}
											alt={product.name}
											fill
											className="object-cover group-hover:scale-105 transition-transform duration-300"
										/>
										{product.salePrice && (
											<div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
												-{Math.round(((product.price - product.salePrice) / product.price) * 100)}%
											</div>
										)}
										<div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
											<Button size="sm" variant="secondary" className="h-8 w-8 p-0">
												<Heart className="h-4 w-4" />
											</Button>
										</div>
									</div>
								</Link>

								<CardContent className={`p-4 ${viewMode === 'list' ? 'flex-1' : ''}`}>
									<Link href={`/products/${product.slug}`}>
										<h3 className="font-medium text-sm mb-2 line-clamp-2 hover:text-pink-600 transition-colors">
											{product.name}
										</h3>
									</Link>

									<div className="flex items-center gap-1 mb-2">
										{renderStars(product.avgRating)}
										<span className="text-xs text-muted-foreground ml-1">
											({product.reviewCount})
										</span>
									</div>

									<div className="flex items-center justify-between">
										<div className="space-y-1">
											{product.salePrice ? (
												<>
													<div className="text-lg font-bold text-pink-600">
														{formatPrice(product.salePrice)}
													</div>
													<div className="text-sm text-muted-foreground line-through">
														{formatPrice(product.price)}
													</div>
												</>
											) : (
												<div className="text-lg font-bold">
													{formatPrice(product.price)}
												</div>
											)}
										</div>

										<Button
											size="sm"
											className="bg-pink-600 hover:bg-pink-700 h-8 w-8 p-0"
											onClick={(e) => {
												e.preventDefault();
												// Add to cart logic here
												toast.success('Đã thêm vào giỏ hàng');
											}}
										>
											<ShoppingCart className="h-4 w-4" />
										</Button>
									</div>

									<div className="text-xs text-muted-foreground mt-2">
										{product.category.name}
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				)}

				{/* Pagination */}
				{pagination.pages > 1 && (
					<div className="flex justify-center items-center gap-2 mt-8">
						<Button
							variant="outline"
							disabled={pagination.page === 1}
							onClick={() => setPagination({ ...pagination, page: pagination.page - 1 })}
						>
							Trước
						</Button>

						{Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
							const page = i + 1;
							return (
								<Button
									key={page}
									variant={pagination.page === page ? 'default' : 'outline'}
									onClick={() => setPagination({ ...pagination, page })}
								>
									{page}
								</Button>
							);
						})}

						<Button
							variant="outline"
							disabled={pagination.page === pagination.pages}
							onClick={() => setPagination({ ...pagination, page: pagination.page + 1 })}
						>
							Sau
						</Button>
					</div>
				)}
			</main>

			<Footer />
		</div>
	);
}
