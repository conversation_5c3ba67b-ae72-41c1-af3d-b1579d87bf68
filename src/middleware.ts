import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
	function middleware(req) {
		const { pathname } = req.nextUrl;
		const token = req.nextauth.token;

		// Admin routes protection
		if (pathname.startsWith('/admin')) {
			if (!token) {
				return NextResponse.redirect(new URL('/auth/signin', req.url));
			}

			if (token.role !== 'ADMIN') {
				return NextResponse.redirect(new URL('/', req.url));
			}
		}

		// User protected routes
		if (pathname.startsWith('/profile') || 
			pathname.startsWith('/orders') || 
			pathname.startsWith('/checkout')) {
			if (!token) {
				return NextResponse.redirect(new URL('/auth/signin', req.url));
			}
		}

		// Redirect authenticated users away from auth pages
		if (token && (pathname.startsWith('/auth/signin') || pathname.startsWith('/auth/signup'))) {
			if (token.role === 'ADMIN') {
				return NextResponse.redirect(new URL('/admin', req.url));
			}
			return NextResponse.redirect(new URL('/', req.url));
		}

		return NextResponse.next();
	},
	{
		callbacks: {
			authorized: ({ token, req }) => {
				const { pathname } = req.nextUrl;

				// Allow access to public routes
				if (
					pathname.startsWith('/auth') ||
					pathname.startsWith('/api/auth') ||
					pathname.startsWith('/api/products') ||
					pathname.startsWith('/api/categories') ||
					pathname === '/' ||
					pathname.startsWith('/products') ||
					pathname.startsWith('/categories') ||
					pathname.startsWith('/_next') ||
					pathname.startsWith('/images') ||
					pathname.includes('.')
				) {
					return true;
				}

				// Require authentication for protected routes
				return !!token;
			},
		},
	}
);

export const config = {
	matcher: [
		/*
		 * Match all request paths except for the ones starting with:
		 * - api/auth (NextAuth.js routes)
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico (favicon file)
		 * - public files (images, etc.)
		 */
		'/((?!api/auth|_next/static|_next/image|favicon.ico|images|manifest.json).*)',
	],
};
