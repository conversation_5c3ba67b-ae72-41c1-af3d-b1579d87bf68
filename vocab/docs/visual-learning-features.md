# Visual Learning Features - Development Plan

## Overview
Implement comprehensive visual learning features including image-word association, visual memory palace, automated infographic generation, and visual content management to enhance learning through visual memory techniques.

## Technical Architecture

### Core Components

#### 1. Visual Content Engine
- **Location**: `src/backend/services/visual-content.service.ts`
- **Purpose**: Manage visual content creation and association
- **Features**:
  - Image-word association
  - Visual content generation
  - Memory palace construction
  - Visual learning analytics

#### 2. Memory Palace Builder
- **Location**: `src/backend/services/memory-palace.service.ts`
- **Purpose**: Create and manage visual memory palaces
- **Features**:
  - Spatial memory organization
  - Route planning
  - Visual anchoring
  - Progress tracking

#### 3. Infographic Generator
- **Location**: `src/backend/services/infographic.service.ts`
- **Purpose**: Automatically generate educational infographics
- **Features**:
  - Template-based generation
  - Data visualization
  - Custom styling
  - Export capabilities

## Database Schema Extensions

### New Tables

```prisma
model VisualContent {
  id              String   @id @default(uuid())
  content_type    String   // 'image', 'infographic', 'diagram', 'memory_palace'
  title           String
  description     String?
  image_url       String
  thumbnail_url   String?
  alt_text        String
  tags            String[] // Visual tags for categorization
  difficulty      String   // 'beginner', 'intermediate', 'advanced'
  language        String   // 'EN', 'VI'
  usage_count     Int      @default(0)
  effectiveness_score Float @default(0.0) // How effective for learning
  created_by      String?  // User who created it
  is_public       Boolean  @default(false)
  is_approved     Boolean  @default(false)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  creator User? @relation(fields: [created_by], references: [id])
  
  @@index([content_type])
  @@index([difficulty])
  @@index([language])
  @@index([is_public])
  @@index([effectiveness_score])
}

model ImageWordAssociation {
  id              String   @id @default(uuid())
  word_id         String
  visual_content_id String
  association_type String  // 'direct', 'metaphor', 'context', 'mnemonic'
  strength        Float    @default(0.5) // 0.0-1.0
  user_id         String?  // If user-specific association
  confidence      Float    @default(0.8) // Confidence in association
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  visual_content VisualContent @relation(fields: [visual_content_id], references: [id])
  user User? @relation(fields: [user_id], references: [id])
  
  @@unique([word_id, visual_content_id, user_id])
  @@index([word_id])
  @@index([visual_content_id])
  @@index([user_id])
  @@index([association_type])
}

model MemoryPalace {
  id              String   @id @default(uuid())
  user_id         String
  name            String
  description     String
  palace_type     String   // 'house', 'route', 'building', 'custom'
  layout_data     Json     // Spatial layout information
  total_locations Int      @default(0)
  used_locations  Int      @default(0)
  effectiveness   Float    @default(0.0) // User performance with this palace
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([palace_type])
  @@index([is_active])
}

model MemoryLocation {
  id              String   @id @default(uuid())
  palace_id       String
  location_name   String   // 'front_door', 'kitchen', 'bedroom_1'
  position_x      Float    // X coordinate in palace
  position_y      Float    // Y coordinate in palace
  position_z      Float?   // Z coordinate for 3D palaces
  description     String
  visual_cues     Json     // Visual elements at this location
  associated_content Json  // Content stored at this location
  visit_count     Int      @default(0)
  success_rate    Float    @default(0.0)
  created_at      DateTime @default(now())
  
  palace MemoryPalace @relation(fields: [palace_id], references: [id])
  
  @@index([palace_id])
  @@index([position_x, position_y])
}

model VisualLearningSession {
  id              String   @id @default(uuid())
  user_id         String
  session_type    String   // 'image_association', 'memory_palace', 'infographic_study'
  content_ids     String[] // Visual content used in session
  duration        Int      // Session duration in minutes
  items_studied   Int      @default(0)
  accuracy        Float    @default(0.0)
  engagement_score Float   @default(0.0) // How engaged user was
  visual_preference Json   // User's visual preferences during session
  created_at      DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([session_type])
  @@index([created_at])
}

model InfographicTemplate {
  id              String   @id @default(uuid())
  name            String
  description     String
  template_type   String   // 'vocabulary', 'grammar', 'comparison', 'process'
  layout_config   Json     // Template layout configuration
  style_config    Json     // Colors, fonts, styling
  data_schema     Json     // Expected data structure
  preview_url     String?
  usage_count     Int      @default(0)
  rating          Float?   // User rating
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  
  @@index([template_type])
  @@index([is_active])
  @@index([rating])
}

model GeneratedInfographic {
  id              String   @id @default(uuid())
  user_id         String
  template_id     String
  title           String
  content_data    Json     // Data used to generate infographic
  generated_url   String   // URL of generated infographic
  thumbnail_url   String?
  generation_time Int      // Time taken to generate (ms)
  view_count      Int      @default(0)
  download_count  Int      @default(0)
  is_public       Boolean  @default(false)
  created_at      DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  template InfographicTemplate @relation(fields: [template_id], references: [id])
  
  @@index([user_id])
  @@index([template_id])
  @@index([is_public])
  @@index([created_at])
}

model VisualLearningPreference {
  id              String   @id @default(uuid())
  user_id         String   @unique
  preferred_style String   // 'realistic', 'cartoon', 'abstract', 'diagram'
  color_preference Json    // Preferred color schemes
  layout_preference String // 'grid', 'flow', 'hierarchical'
  animation_preference Boolean @default(true)
  complexity_level String  // 'simple', 'moderate', 'complex'
  cultural_context String  // Cultural context for images
  accessibility_needs Json // Visual accessibility requirements
  updated_at      DateTime @updatedAt
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
}
```

## Implementation Plan

### Phase 1: Image-Word Association System (Week 1-2)

#### 1.1 Visual Content Management
```typescript
// src/backend/services/visual-content.service.ts
export interface VisualContentService {
  createImageWordAssociation(
    wordId: string,
    imageData: ImageData,
    associationType: AssociationType
  ): Promise<ImageWordAssociation>;
  
  findVisualContentForWord(
    wordId: string,
    userId?: string
  ): Promise<VisualContent[]>;
  
  generateImageSuggestions(
    word: string,
    context?: string
  ): Promise<ImageSuggestion[]>;
  
  analyzeImageEffectiveness(
    associationId: string,
    userPerformance: PerformanceData[]
  ): Promise<EffectivenessAnalysis>;
}

enum AssociationType {
  DIRECT = 'direct',           // Direct visual representation
  METAPHOR = 'metaphor',       // Metaphorical association
  CONTEXT = 'context',         // Contextual scene
  MNEMONIC = 'mnemonic'        // Mnemonic device
}

class ImageAssociationEngine {
  async createAssociation(
    wordId: string,
    imageData: ImageData,
    associationType: AssociationType,
    userId?: string
  ): Promise<ImageWordAssociation> {
    // Analyze image content
    const imageAnalysis = await this.analyzeImage(imageData.url);
    
    // Calculate association strength
    const strength = await this.calculateAssociationStrength(
      wordId,
      imageAnalysis,
      associationType
    );
    
    // Create visual content record
    const visualContent = await this.createVisualContent({
      content_type: 'image',
      title: imageData.title,
      description: imageData.description,
      image_url: imageData.url,
      thumbnail_url: imageData.thumbnailUrl,
      alt_text: imageData.altText,
      tags: imageAnalysis.tags,
      effectiveness_score: strength
    });
    
    // Create association
    return await this.prisma.imageWordAssociation.create({
      data: {
        word_id: wordId,
        visual_content_id: visualContent.id,
        association_type: associationType,
        strength,
        user_id: userId,
        confidence: imageAnalysis.confidence
      }
    });
  }
  
  private async analyzeImage(imageUrl: string): Promise<ImageAnalysis> {
    // Use computer vision API to analyze image
    const analysis = await this.visionService.analyzeImage(imageUrl);
    
    return {
      objects: analysis.objects,
      scenes: analysis.scenes,
      colors: analysis.dominantColors,
      emotions: analysis.emotions,
      tags: analysis.tags,
      confidence: analysis.confidence
    };
  }
  
  private async calculateAssociationStrength(
    wordId: string,
    imageAnalysis: ImageAnalysis,
    associationType: AssociationType
  ): Promise<number> {
    const word = await this.getWord(wordId);
    
    // Calculate semantic similarity
    const semanticSimilarity = await this.calculateSemanticSimilarity(
      word.term,
      imageAnalysis.tags
    );
    
    // Adjust based on association type
    const typeMultiplier = {
      [AssociationType.DIRECT]: 1.0,
      [AssociationType.METAPHOR]: 0.8,
      [AssociationType.CONTEXT]: 0.7,
      [AssociationType.MNEMONIC]: 0.6
    };
    
    return Math.min(1.0, semanticSimilarity * typeMultiplier[associationType]);
  }
}
```

#### 1.2 Smart Image Suggestions
```typescript
class ImageSuggestionEngine {
  async generateSuggestions(
    word: string,
    context?: string,
    userPreferences?: VisualLearningPreference
  ): Promise<ImageSuggestion[]> {
    const suggestions: ImageSuggestion[] = [];
    
    // Get direct visual representations
    const directImages = await this.searchDirectImages(word);
    suggestions.push(...directImages.map(img => ({
      ...img,
      type: AssociationType.DIRECT,
      relevanceScore: 0.9
    })));
    
    // Get metaphorical representations
    const metaphorImages = await this.searchMetaphorImages(word, context);
    suggestions.push(...metaphorImages.map(img => ({
      ...img,
      type: AssociationType.METAPHOR,
      relevanceScore: 0.7
    })));
    
    // Get contextual images
    const contextImages = await this.searchContextualImages(word, context);
    suggestions.push(...contextImages.map(img => ({
      ...img,
      type: AssociationType.CONTEXT,
      relevanceScore: 0.6
    })));
    
    // Filter and rank based on user preferences
    return this.rankSuggestions(suggestions, userPreferences);
  }
  
  private async searchDirectImages(word: string): Promise<ImageResult[]> {
    // Search for direct visual representations
    const searchQueries = [
      word,
      `${word} illustration`,
      `${word} icon`,
      `${word} simple drawing`
    ];
    
    const results: ImageResult[] = [];
    for (const query of searchQueries) {
      const images = await this.imageSearchService.search(query, {
        type: 'illustration',
        license: 'creative_commons',
        size: 'medium'
      });
      results.push(...images);
    }
    
    return this.deduplicateImages(results);
  }
  
  private rankSuggestions(
    suggestions: ImageSuggestion[],
    preferences?: VisualLearningPreference
  ): ImageSuggestion[] {
    return suggestions
      .map(suggestion => ({
        ...suggestion,
        finalScore: this.calculateFinalScore(suggestion, preferences)
      }))
      .sort((a, b) => b.finalScore - a.finalScore)
      .slice(0, 20); // Return top 20 suggestions
  }
}
```

### Phase 2: Memory Palace Builder (Week 3-4)

#### 2.1 Memory Palace Construction
```typescript
// src/backend/services/memory-palace.service.ts
export interface MemoryPalaceService {
  createMemoryPalace(
    userId: string,
    palaceData: CreatePalaceData
  ): Promise<MemoryPalace>;
  
  addLocationToPalace(
    palaceId: string,
    locationData: LocationData
  ): Promise<MemoryLocation>;
  
  associateContentWithLocation(
    locationId: string,
    contentData: ContentAssociation
  ): Promise<void>;
  
  generateOptimalRoute(
    palaceId: string,
    contentIds: string[]
  ): Promise<MemoryRoute>;
}

class MemoryPalaceBuilder {
  async createPalace(
    userId: string,
    palaceData: CreatePalaceData
  ): Promise<MemoryPalace> {
    const palace = await this.prisma.memoryPalace.create({
      data: {
        user_id: userId,
        name: palaceData.name,
        description: palaceData.description,
        palace_type: palaceData.type,
        layout_data: this.generateLayoutData(palaceData.type)
      }
    });
    
    // Create default locations based on palace type
    await this.createDefaultLocations(palace.id, palaceData.type);
    
    return palace;
  }
  
  private generateLayoutData(palaceType: string): any {
    const layouts = {
      house: {
        rooms: [
          { name: 'entrance', x: 0, y: 0, connections: ['hallway'] },
          { name: 'hallway', x: 1, y: 0, connections: ['entrance', 'living_room', 'kitchen'] },
          { name: 'living_room', x: 2, y: 0, connections: ['hallway', 'bedroom'] },
          { name: 'kitchen', x: 1, y: 1, connections: ['hallway', 'dining_room'] },
          { name: 'dining_room', x: 2, y: 1, connections: ['kitchen', 'bedroom'] },
          { name: 'bedroom', x: 3, y: 0, connections: ['living_room', 'dining_room'] }
        ],
        dimensions: { width: 4, height: 2 }
      },
      route: {
        waypoints: [
          { name: 'start', x: 0, y: 0, description: 'Starting point' },
          { name: 'landmark_1', x: 1, y: 0, description: 'First landmark' },
          { name: 'landmark_2', x: 2, y: 1, description: 'Second landmark' },
          { name: 'landmark_3', x: 3, y: 1, description: 'Third landmark' },
          { name: 'destination', x: 4, y: 0, description: 'Final destination' }
        ],
        path: ['start', 'landmark_1', 'landmark_2', 'landmark_3', 'destination']
      }
    };
    
    return layouts[palaceType] || layouts.house;
  }
  
  async associateContentWithLocation(
    locationId: string,
    contentData: ContentAssociation
  ): Promise<void> {
    const location = await this.getLocation(locationId);
    const currentContent = location.associated_content as any[] || [];
    
    // Create vivid association
    const association = {
      contentId: contentData.contentId,
      contentType: contentData.contentType,
      visualAnchor: contentData.visualAnchor,
      story: contentData.story,
      sensoryDetails: contentData.sensoryDetails,
      createdAt: new Date()
    };
    
    currentContent.push(association);
    
    await this.prisma.memoryLocation.update({
      where: { id: locationId },
      data: {
        associated_content: currentContent
      }
    });
    
    // Update palace statistics
    await this.updatePalaceStats(location.palace_id);
  }
}
```

#### 2.2 Memory Palace Navigation
```typescript
class MemoryPalaceNavigator {
  async generateOptimalRoute(
    palaceId: string,
    contentIds: string[]
  ): Promise<MemoryRoute> {
    const palace = await this.getPalace(palaceId);
    const locations = await this.getPalaceLocations(palaceId);
    
    // Find locations with associated content
    const contentLocations = locations.filter(loc => 
      this.hasAssociatedContent(loc, contentIds)
    );
    
    // Generate optimal path using traveling salesman algorithm
    const optimalPath = this.calculateOptimalPath(contentLocations);
    
    // Create route with detailed instructions
    const route: MemoryRoute = {
      palaceId,
      totalLocations: optimalPath.length,
      estimatedTime: this.estimateRouteTime(optimalPath),
      steps: optimalPath.map((location, index) => ({
        stepNumber: index + 1,
        locationId: location.id,
        locationName: location.location_name,
        description: location.description,
        visualCues: location.visual_cues,
        associatedContent: this.getRelevantContent(location, contentIds),
        navigationInstructions: this.generateNavigationInstructions(
          optimalPath,
          index
        )
      }))
    };
    
    return route;
  }
  
  private calculateOptimalPath(locations: MemoryLocation[]): MemoryLocation[] {
    // Simple nearest neighbor algorithm for small sets
    if (locations.length <= 10) {
      return this.nearestNeighborPath(locations);
    }
    
    // Use genetic algorithm for larger sets
    return this.geneticAlgorithmPath(locations);
  }
  
  private generateNavigationInstructions(
    path: MemoryLocation[],
    currentIndex: number
  ): string {
    if (currentIndex === 0) {
      return `Start at ${path[0].location_name}. ${path[0].description}`;
    }
    
    if (currentIndex === path.length - 1) {
      return `Finally, arrive at ${path[currentIndex].location_name}.`;
    }
    
    const current = path[currentIndex];
    const next = path[currentIndex + 1];
    
    return `From ${current.location_name}, move to ${next.location_name}. ${next.description}`;
  }
}
```

### Phase 3: Automated Infographic Generation (Week 5-6)

#### 3.1 Infographic Template System
```typescript
// src/backend/services/infographic.service.ts
export interface InfographicService {
  generateInfographic(
    userId: string,
    templateId: string,
    data: InfographicData
  ): Promise<GeneratedInfographic>;
  
  createCustomTemplate(
    templateData: TemplateData
  ): Promise<InfographicTemplate>;
  
  getTemplateRecommendations(
    contentType: string,
    dataStructure: any
  ): Promise<InfographicTemplate[]>;
}

class InfographicGenerator {
  async generateInfographic(
    userId: string,
    templateId: string,
    data: InfographicData
  ): Promise<GeneratedInfographic> {
    const template = await this.getTemplate(templateId);
    const userPreferences = await this.getUserVisualPreferences(userId);
    
    // Validate data against template schema
    this.validateDataSchema(data, template.data_schema);
    
    // Generate infographic
    const generationStart = Date.now();
    const infographicUrl = await this.renderInfographic(
      template,
      data,
      userPreferences
    );
    const generationTime = Date.now() - generationStart;
    
    // Create thumbnail
    const thumbnailUrl = await this.generateThumbnail(infographicUrl);
    
    // Save generated infographic
    const generated = await this.prisma.generatedInfographic.create({
      data: {
        user_id: userId,
        template_id: templateId,
        title: data.title,
        content_data: data,
        generated_url: infographicUrl,
        thumbnail_url: thumbnailUrl,
        generation_time: generationTime
      }
    });
    
    // Update template usage
    await this.updateTemplateUsage(templateId);
    
    return generated;
  }
  
  private async renderInfographic(
    template: InfographicTemplate,
    data: InfographicData,
    preferences: VisualLearningPreference
  ): Promise<string> {
    const canvas = this.createCanvas(template.layout_config);
    
    // Apply user preferences to styling
    const styling = this.mergeStyles(
      template.style_config,
      preferences.color_preference,
      preferences.preferred_style
    );
    
    // Render components based on template
    await this.renderBackground(canvas, styling);
    await this.renderTitle(canvas, data.title, styling);
    await this.renderContent(canvas, data.content, template.layout_config);
    await this.renderVisualElements(canvas, data.visualElements, styling);
    
    // Export to image
    return await this.exportCanvas(canvas);
  }
  
  private async renderContent(
    canvas: Canvas,
    content: ContentItem[],
    layout: LayoutConfig
  ): Promise<void> {
    for (const [index, item] of content.entries()) {
      const position = this.calculateItemPosition(index, layout);
      
      switch (item.type) {
        case 'text':
          await this.renderText(canvas, item.data, position);
          break;
        case 'image':
          await this.renderImage(canvas, item.data, position);
          break;
        case 'chart':
          await this.renderChart(canvas, item.data, position);
          break;
        case 'diagram':
          await this.renderDiagram(canvas, item.data, position);
          break;
      }
    }
  }
}
```

### Phase 4: Visual Learning Analytics (Week 7-8)

#### 4.1 Visual Learning Effectiveness Tracking
```typescript
class VisualLearningAnalytics {
  async trackVisualLearningSession(
    userId: string,
    sessionData: VisualLearningSessionData
  ): Promise<void> {
    const session = await this.prisma.visualLearningSession.create({
      data: {
        user_id: userId,
        session_type: sessionData.type,
        content_ids: sessionData.contentIds,
        duration: sessionData.duration,
        items_studied: sessionData.itemsStudied,
        accuracy: sessionData.accuracy,
        engagement_score: sessionData.engagementScore,
        visual_preference: sessionData.visualPreferences
      }
    });
    
    // Update content effectiveness scores
    await this.updateContentEffectiveness(sessionData.contentIds, sessionData.accuracy);
    
    // Update user visual preferences
    await this.updateUserVisualPreferences(userId, sessionData.visualPreferences);
  }
  
  async analyzeVisualLearningEffectiveness(
    userId: string
  ): Promise<VisualLearningAnalysis> {
    const sessions = await this.getUserVisualSessions(userId);
    const associations = await this.getUserImageAssociations(userId);
    
    return {
      overallEffectiveness: this.calculateOverallEffectiveness(sessions),
      preferredVisualTypes: this.identifyPreferredVisualTypes(sessions),
      mostEffectiveAssociations: this.findMostEffectiveAssociations(associations),
      improvementAreas: this.identifyImprovementAreas(sessions),
      recommendations: this.generateVisualRecommendations(sessions, associations)
    };
  }
  
  private calculateOverallEffectiveness(
    sessions: VisualLearningSession[]
  ): number {
    if (sessions.length === 0) return 0;
    
    const weightedSum = sessions.reduce((sum, session) => {
      const weight = session.duration / 60; // Weight by duration in hours
      return sum + (session.accuracy * session.engagement_score * weight);
    }, 0);
    
    const totalWeight = sessions.reduce((sum, session) => 
      sum + (session.duration / 60), 0
    );
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }
}
```

## Frontend Integration

### Visual Learning Dashboard
```typescript
// src/components/ui/visual-learning-dashboard.tsx
export function VisualLearningDashboard({ userId }: { userId: string }) {
  const { visualContent, memoryPalaces, infographics } = useVisualLearning(userId);
  const { preferences } = useVisualPreferences(userId);
  
  return (
    <div className="visual-learning-dashboard">
      <div className="dashboard-header">
        <h2>Visual Learning</h2>
        <VisualPreferencesPanel preferences={preferences} />
      </div>
      
      <div className="visual-content-grid">
        <ImageAssociationPanel content={visualContent} />
        <MemoryPalaceBuilder palaces={memoryPalaces} />
        <InfographicGallery infographics={infographics} />
      </div>
      
      <div className="visual-analytics">
        <VisualLearningAnalytics userId={userId} />
      </div>
    </div>
  );
}
```

## Success Criteria

### Learning Effectiveness
- 40% improvement in visual learners' retention rates
- 35% increase in engagement for visual content
- 50% better recall for image-associated words
- 30% improvement in memory palace navigation accuracy

### Content Quality
- 85%+ user satisfaction with generated infographics
- 90%+ accuracy in image-word associations
- 75%+ effectiveness score for memory palaces
- 80%+ user preference match for visual content

## Timeline

- **Week 1-2**: Image-word association system and visual content management
- **Week 3-4**: Memory palace builder and navigation system
- **Week 5-6**: Automated infographic generation and templates
- **Week 7-8**: Visual learning analytics and optimization
- **Week 9**: Testing and user experience refinement
- **Week 10**: Deployment and visual content library expansion
