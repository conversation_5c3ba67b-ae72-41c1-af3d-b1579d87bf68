# Cultural Notes Development Plan

## Overview

Implement a comprehensive cultural context system that provides learners with cultural insights, social nuances, and contextual understanding of vocabulary usage across different cultures and regions.

## Technical Architecture

### Database Schema Extensions

#### Cultural Context Models

```prisma
model CulturalNote {
  id              String              @id @default(uuid())
  title           String
  content         String
  type            CulturalNoteType
  category        CulturalCategory
  language        Language            @default(EN)
  region          String?             // Specific region/country
  culture         String?             // Cultural group
  difficulty      Difficulty          @default(BEGINNER)
  tags            String[]
  isVerified      Boolean             @default(false)
  verifiedBy      String?
  verifiedAt      DateTime?
  upvotes         Int                 @default(0)
  downvotes       Int                 @default(0)
  viewCount       Int                 @default(0)
  createdBy       String
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  creator         User                @relation("CreatedCulturalNotes", fields: [createdBy], references: [id])
  verifier        User?               @relation("VerifiedCulturalNotes", fields: [verifiedBy], references: [id])
  words           CulturalNoteWord[]
  examples        CulturalExample[]
  votes           CulturalNoteVote[]
  comments        CulturalNoteComment[]

  @@index([type, category])
  @@index([language, region])
  @@index([isVerified])
  @@map("cultural_notes")
}

model CulturalNoteWord {
  id              String              @id @default(uuid())
  culturalNoteId  String
  wordId          String
  relevance       Float               @default(0.5) // 0-1 relevance score
  context         String?             // Specific context for this word

  culturalNote    CulturalNote        @relation(fields: [culturalNoteId], references: [id], onDelete: Cascade)
  word            Word                @relation(fields: [wordId], references: [id], onDelete: Cascade)

  @@unique([culturalNoteId, wordId])
  @@index([wordId])
  @@map("cultural_note_words")
}

model CulturalExample {
  id              String              @id @default(uuid())
  culturalNoteId  String
  situation       String              // Situation description
  appropriateUsage String             // Appropriate usage
  inappropriateUsage String?          // What to avoid
  explanation     String?             // Why this matters
  severity        SeverityLevel       @default(MEDIUM)

  culturalNote    CulturalNote        @relation(fields: [culturalNoteId], references: [id], onDelete: Cascade)

  @@index([culturalNoteId])
  @@map("cultural_examples")
}

model RegionalVariation {
  id              String              @id @default(uuid())
  wordId          String
  region          String              // Region/country code
  variation       String              // Regional variation of the word
  pronunciation   String?             // Regional pronunciation
  usage           String?             // Usage notes for this region
  frequency       Float               @default(0.5) // How common in this region
  formality       FormalityLevel      @default(NEUTRAL)
  isOfficial      Boolean             @default(false)

  word            Word                @relation(fields: [wordId], references: [id], onDelete: Cascade)

  @@unique([wordId, region, variation])
  @@index([wordId])
  @@index([region])
  @@map("regional_variations")
}

model IdiomaticExpression {
  id              String              @id @default(uuid())
  expression      String
  meaning         String
  literalTranslation String?
  origin          String?             // Historical/cultural origin
  usage           String              // When and how to use
  language        Language            @default(EN)
  region          String?
  difficulty      Difficulty          @default(INTERMEDIATE)
  frequency       Float               @default(0.5)
  isActive        Boolean             @default(true) // Still in use
  tags            String[]
  createdBy       String
  createdAt       DateTime            @default(now())

  creator         User                @relation(fields: [createdBy], references: [id])
  words           IdiomaticExpressionWord[]
  examples        IdiomaticExample[]

  @@index([language, region])
  @@index([difficulty])
  @@map("idiomatic_expressions")
}

model IdiomaticExpressionWord {
  id              String              @id @default(uuid())
  expressionId    String
  wordId          String
  position        Int                 // Position in expression
  isKey           Boolean             @default(false) // Key word in expression

  expression      IdiomaticExpression @relation(fields: [expressionId], references: [id], onDelete: Cascade)
  word            Word                @relation(fields: [wordId], references: [id], onDelete: Cascade)

  @@unique([expressionId, wordId])
  @@index([wordId])
  @@map("idiomatic_expression_words")
}

model IdiomaticExample {
  id              String              @id @default(uuid())
  expressionId    String
  context         String              // Context where it's used
  example         String              // Example sentence
  translation     String?             // Translation if needed
  appropriateness String?             // When appropriate to use

  expression      IdiomaticExpression @relation(fields: [expressionId], references: [id], onDelete: Cascade)

  @@index([expressionId])
  @@map("idiomatic_examples")
}

model CulturalNoteVote {
  id              String              @id @default(uuid())
  culturalNoteId  String
  userId          String
  voteType        VoteType
  createdAt       DateTime            @default(now())

  culturalNote    CulturalNote        @relation(fields: [culturalNoteId], references: [id], onDelete: Cascade)
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([culturalNoteId, userId])
  @@index([culturalNoteId])
  @@map("cultural_note_votes")
}

model CulturalNoteComment {
  id              String              @id @default(uuid())
  culturalNoteId  String
  userId          String
  content         String
  parentId        String?             // For nested comments
  isExpertComment Boolean             @default(false)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  culturalNote    CulturalNote        @relation(fields: [culturalNoteId], references: [id], onDelete: Cascade)
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent          CulturalNoteComment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies         CulturalNoteComment[] @relation("CommentReplies")

  @@index([culturalNoteId])
  @@index([userId])
  @@map("cultural_note_comments")
}

model UserCulturalProfile {
  id              String              @id @default(uuid())
  userId          String              @unique
  nativeRegion    String?             // User's native region
  targetRegions   String[]            // Regions they're learning about
  culturalLevel   CulturalLevel       @default(BEGINNER)
  interests       String[]            // Cultural interests
  preferences     Json?               // Cultural learning preferences
  expertiseAreas  String[]            // Areas where user is expert
  isExpert        Boolean             @default(false)

  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("user_cultural_profiles")
}

enum CulturalNoteType {
  ETIQUETTE
  SOCIAL_NORMS
  BUSINESS_CULTURE
  HISTORICAL_CONTEXT
  RELIGIOUS_CONTEXT
  GENERATIONAL_DIFFERENCES
  TABOOS
  CELEBRATIONS
  FOOD_CULTURE
  COMMUNICATION_STYLE
}

enum CulturalCategory {
  FORMAL_INFORMAL
  GENERATIONAL
  PROFESSIONAL
  SOCIAL
  RELIGIOUS
  HISTORICAL
  REGIONAL
  SEASONAL
}

enum SeverityLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum FormalityLevel {
  VERY_FORMAL
  FORMAL
  NEUTRAL
  INFORMAL
  VERY_INFORMAL
}

enum CulturalLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum VoteType {
  UPVOTE
  DOWNVOTE
}
```

#### User and Word Model Extensions

```prisma
model User {
  // ... existing fields
  createdCulturalNotes    CulturalNote[]        @relation("CreatedCulturalNotes")
  verifiedCulturalNotes   CulturalNote[]        @relation("VerifiedCulturalNotes")
  culturalNoteVotes       CulturalNoteVote[]
  culturalNoteComments    CulturalNoteComment[]
  idiomaticExpressions    IdiomaticExpression[]
  culturalProfile         UserCulturalProfile?
  isCulturalExpert        Boolean               @default(false)
}

model Word {
  // ... existing fields
  culturalNotes           CulturalNoteWord[]
  regionalVariations      RegionalVariation[]
  idiomaticExpressions    IdiomaticExpressionWord[]
}
```

### Backend Implementation

#### Services

**Cultural Context Service** (`src/backend/services/cultural-context.service.ts`)

```typescript
export interface CulturalContextService {
	getCulturalNotes(wordId: string, region?: string): Promise<CulturalNote[]>;
	createCulturalNote(userId: string, noteData: CreateCulturalNoteDto): Promise<CulturalNote>;
	getRegionalVariations(wordId: string): Promise<RegionalVariation[]>;
	getIdiomaticExpressions(wordId: string, region?: string): Promise<IdiomaticExpression[]>;
	voteCulturalNote(userId: string, noteId: string, voteType: VoteType): Promise<CulturalNoteVote>;
	verifyCulturalNote(expertId: string, noteId: string): Promise<CulturalNote>;
	getUserCulturalProfile(userId: string): Promise<UserCulturalProfile>;
	updateCulturalProfile(
		userId: string,
		profileData: UpdateCulturalProfileDto
	): Promise<UserCulturalProfile>;
}

export class CulturalContextServiceImpl implements CulturalContextService {
	constructor(
		private getCulturalNoteRepository: () => CulturalNoteRepository,
		private getRegionalVariationRepository: () => RegionalVariationRepository,
		private getIdiomaticExpressionRepository: () => IdiomaticExpressionRepository,
		private getUserCulturalProfileRepository: () => UserCulturalProfileRepository,
		private getAIAnalysisService: () => AIAnalysisService
	) {}

	async getCulturalNotes(wordId: string, region?: string): Promise<CulturalNote[]> {
		const notes = await this.getCulturalNoteRepository().findByWordId(wordId, {
			region,
			isVerified: true,
			orderBy: 'relevance',
		});

		// Enrich with user-specific context if available
		return this.enrichWithUserContext(notes, region);
	}

	async createCulturalNote(
		userId: string,
		noteData: CreateCulturalNoteDto
	): Promise<CulturalNote> {
		// Validate cultural note content
		await this.validateCulturalNote(noteData);

		// Check for duplicates
		const existingNote = await this.getCulturalNoteRepository().findSimilar(
			noteData.title,
			noteData.content
		);

		if (existingNote) {
			throw new ValidationError('Similar cultural note already exists');
		}

		const note = await this.getCulturalNoteRepository().create({
			...noteData,
			createdBy: userId,
		});

		// Auto-analyze for quality and relevance
		await this.analyzeNoteQuality(note.id);

		return note;
	}

	async getRegionalVariations(wordId: string): Promise<RegionalVariation[]> {
		const variations = await this.getRegionalVariationRepository().findByWordId(wordId);

		// Sort by frequency and official status
		return variations.sort((a, b) => {
			if (a.isOfficial !== b.isOfficial) {
				return a.isOfficial ? -1 : 1;
			}
			return b.frequency - a.frequency;
		});
	}

	async getIdiomaticExpressions(wordId: string, region?: string): Promise<IdiomaticExpression[]> {
		const expressions = await this.getIdiomaticExpressionRepository().findByWordId(wordId, {
			region,
			isActive: true,
		});

		// Filter by difficulty and frequency
		return expressions.filter((expr) => expr.frequency > 0.1);
	}

	async voteCulturalNote(
		userId: string,
		noteId: string,
		voteType: VoteType
	): Promise<CulturalNoteVote> {
		const note = await this.getCulturalNoteRepository().findById(noteId);
		if (!note) {
			throw new NotFoundError('Cultural note not found');
		}

		if (note.createdBy === userId) {
			throw new ValidationError('Cannot vote on your own cultural note');
		}

		// Check for existing vote
		const existingVote = await this.getCulturalNoteVoteRepository().findByNoteAndUser(
			noteId,
			userId
		);

		if (existingVote) {
			if (existingVote.voteType === voteType) {
				// Remove vote if same type
				await this.getCulturalNoteVoteRepository().delete(existingVote.id);
				await this.updateVoteCounts(noteId, voteType, -1);
				return existingVote;
			} else {
				// Update vote type
				const updatedVote = await this.getCulturalNoteVoteRepository().update(
					existingVote.id,
					{ voteType }
				);
				await this.updateVoteCounts(noteId, existingVote.voteType, -1);
				await this.updateVoteCounts(noteId, voteType, 1);
				return updatedVote;
			}
		}

		// Create new vote
		const vote = await this.getCulturalNoteVoteRepository().create({
			culturalNoteId: noteId,
			userId,
			voteType,
		});

		await this.updateVoteCounts(noteId, voteType, 1);
		return vote;
	}

	async verifyCulturalNote(expertId: string, noteId: string): Promise<CulturalNote> {
		// Check if user is cultural expert
		const userProfile = await this.getUserCulturalProfileRepository().findByUserId(expertId);

		if (!userProfile?.isExpert) {
			throw new UnauthorizedError('Only cultural experts can verify notes');
		}

		const note = await this.getCulturalNoteRepository().findById(noteId);
		if (!note) {
			throw new NotFoundError('Cultural note not found');
		}

		if (note.isVerified) {
			throw new ValidationError('Note is already verified');
		}

		return await this.getCulturalNoteRepository().update(noteId, {
			isVerified: true,
			verifiedBy: expertId,
			verifiedAt: new Date(),
		});
	}

	private async validateCulturalNote(noteData: CreateCulturalNoteDto): Promise<void> {
		if (noteData.content.length < 50) {
			throw new ValidationError('Cultural note content must be at least 50 characters');
		}

		if (noteData.examples && noteData.examples.length === 0) {
			throw new ValidationError('At least one example is required');
		}

		// Check for inappropriate content
		const isAppropriate = await this.getAIAnalysisService().checkContentAppropriateness(
			noteData.content
		);

		if (!isAppropriate) {
			throw new ValidationError('Content contains inappropriate material');
		}
	}

	private async analyzeNoteQuality(noteId: string): Promise<void> {
		const note = await this.getCulturalNoteRepository().findById(noteId);
		if (!note) return;

		// AI analysis for quality scoring
		const qualityScore = await this.getAIAnalysisService().analyzeCulturalNoteQuality(note);

		// Auto-verify high-quality notes from experts
		if (qualityScore > 0.8) {
			const userProfile = await this.getUserCulturalProfileRepository().findByUserId(
				note.createdBy
			);

			if (userProfile?.isExpert) {
				await this.getCulturalNoteRepository().update(noteId, {
					isVerified: true,
					verifiedBy: note.createdBy,
					verifiedAt: new Date(),
				});
			}
		}
	}

	private async enrichWithUserContext(
		notes: CulturalNote[],
		region?: string
	): Promise<CulturalNote[]> {
		// Add region-specific context and prioritization
		return notes.map((note) => ({
			...note,
			relevanceScore: this.calculateRelevanceScore(note, region),
			userContext: this.generateUserContext(note, region),
		}));
	}

	private calculateRelevanceScore(note: CulturalNote, userRegion?: string): number {
		let score = 0.5; // Base score

		// Region match bonus
		if (userRegion && note.region === userRegion) {
			score += 0.3;
		}

		// Verification bonus
		if (note.isVerified) {
			score += 0.2;
		}

		// Vote ratio bonus
		const totalVotes = note.upvotes + note.downvotes;
		if (totalVotes > 0) {
			const ratio = note.upvotes / totalVotes;
			score += ratio * 0.2;
		}

		return Math.min(1.0, score);
	}
}
```

### Frontend Implementation

#### Components

**Cultural Context Panel** (`src/components/ui/cultural-context-panel.tsx`)

```typescript
interface CulturalContextPanelProps {
	wordId: string;
	userRegion?: string;
	onNoteCreate?: () => void;
}

export function CulturalContextPanel({
	wordId,
	userRegion,
	onNoteCreate,
}: CulturalContextPanelProps) {
	const [culturalNotes, setCulturalNotes] = useState<CulturalNote[]>([]);
	const [regionalVariations, setRegionalVariations] = useState<RegionalVariation[]>([]);
	const [idiomaticExpressions, setIdiomaticExpressions] = useState<IdiomaticExpression[]>([]);
	const [selectedTab, setSelectedTab] = useState<'notes' | 'variations' | 'idioms'>('notes');
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		loadCulturalContext();
	}, [wordId, userRegion]);

	const loadCulturalContext = async () => {
		setLoading(true);
		try {
			const [notes, variations, expressions] = await Promise.all([
				getCulturalNotesApi(wordId, userRegion),
				getRegionalVariationsApi(wordId),
				getIdiomaticExpressionsApi(wordId, userRegion),
			]);

			setCulturalNotes(notes);
			setRegionalVariations(variations);
			setIdiomaticExpressions(expressions);
		} catch (error) {
			console.error('Failed to load cultural context:', error);
		} finally {
			setLoading(false);
		}
	};

	const tabs = [
		{ id: 'notes', label: 'Cultural Notes', count: culturalNotes.length },
		{ id: 'variations', label: 'Regional Variations', count: regionalVariations.length },
		{ id: 'idioms', label: 'Idioms & Expressions', count: idiomaticExpressions.length },
	];

	if (loading) {
		return (
			<div className="space-y-4">
				{Array.from({ length: 3 }).map((_, i) => (
					<div key={i} className="animate-pulse">
						<div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
						<div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
						<div className="h-3 bg-gray-200 rounded w-5/6"></div>
					</div>
				))}
			</div>
		);
	}

	return (
		<div className="bg-white rounded-lg border border-gray-200">
			{/* Header */}
			<div className="p-4 border-b">
				<div className="flex items-center justify-between">
					<h3 className="text-lg font-semibold text-gray-900">Cultural Context</h3>
					<Button size="sm" onClick={onNoteCreate}>
						<Plus className="w-4 h-4 mr-2" />
						Add Note
					</Button>
				</div>
			</div>

			{/* Tabs */}
			<div className="border-b">
				<nav className="flex space-x-8 px-4">
					{tabs.map((tab) => (
						<button
							key={tab.id}
							onClick={() => setSelectedTab(tab.id as any)}
							className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
								selectedTab === tab.id
									? 'border-blue-500 text-blue-600'
									: 'border-transparent text-gray-500 hover:text-gray-700'
							}`}
						>
							{tab.label}
							{tab.count > 0 && (
								<span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
									{tab.count}
								</span>
							)}
						</button>
					))}
				</nav>
			</div>

			{/* Content */}
			<div className="p-4">
				{selectedTab === 'notes' && (
					<CulturalNotesList
						notes={culturalNotes}
						onVote={(noteId, voteType) => voteCulturalNoteApi(noteId, voteType)}
						onComment={(noteId, content) => addCulturalNoteCommentApi(noteId, content)}
					/>
				)}

				{selectedTab === 'variations' && (
					<RegionalVariationsList
						variations={regionalVariations}
						userRegion={userRegion}
					/>
				)}

				{selectedTab === 'idioms' && (
					<IdiomaticExpressionsList
						expressions={idiomaticExpressions}
						onExpressionClick={(expressionId) => {
							// Navigate to expression details
						}}
					/>
				)}
			</div>
		</div>
	);
}
```

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure

-   Database schema implementation
-   Basic cultural context service
-   Cultural note creation and management

### Phase 2 (Weeks 3-4): Regional & Idiomatic Features

-   Regional variation system
-   Idiomatic expression management
-   Expert verification system

### Phase 3 (Weeks 5-6): Frontend Components

-   Cultural context panel
-   Note creation and voting interface
-   Regional variation display

### Phase 4 (Weeks 7-8): Advanced Features

-   AI-powered content analysis
-   Cultural learning recommendations
-   Community moderation tools

## Success Metrics

-   Cultural note creation and engagement
-   User understanding improvement
-   Regional accuracy validation
-   Expert participation rates
-   Cultural sensitivity awareness

## Future Enhancements

-   Video cultural explanations
-   Interactive cultural scenarios
-   Cross-cultural comparison tools
-   AI-powered cultural insights
-   Real-time cultural trend tracking
