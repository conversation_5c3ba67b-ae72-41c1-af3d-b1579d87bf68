# Real-time Progress Tracking - Development Plan

## Overview
Implement a comprehensive real-time progress tracking system that monitors user learning activities, provides instant feedback, and maintains detailed analytics for continuous learning optimization.

## Technical Architecture

### Core Components

#### 1. Real-time Event Tracking System
- **Location**: `src/backend/services/event-tracking.service.ts`
- **Purpose**: Capture and process learning events in real-time
- **Features**:
  - Event streaming
  - Real-time analytics
  - Progress calculations
  - Achievement detection

#### 2. Progress Analytics Engine
- **Location**: `src/backend/services/progress-analytics.service.ts`
- **Purpose**: Analyze progress data and generate insights
- **Capabilities**:
  - Multi-dimensional progress metrics
  - Trend analysis
  - Performance benchmarking
  - Goal tracking

#### 3. Live Dashboard System
- **Location**: `src/components/ui/live-dashboard.tsx`
- **Purpose**: Real-time progress visualization
- **Features**:
  - Live progress updates
  - Interactive charts
  - Achievement notifications
  - Performance indicators

## Database Schema Extensions

### New Tables

```prisma
model ProgressEvent {
  id              String   @id @default(uuid())
  user_id         String
  event_type      String   // 'word_learned', 'exercise_completed', 'milestone_reached'
  event_data      Json     // Event-specific data
  skill_domain    String   // 'vocabulary', 'grammar', 'reading'
  progress_delta  Float    // Change in progress (0.0-1.0)
  session_id      String
  timestamp       DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([event_type])
  @@index([timestamp])
  @@index([session_id])
}

model ProgressSnapshot {
  id                String   @id @default(uuid())
  user_id           String
  skill_domain      String
  overall_progress  Float    // 0.0-1.0
  sub_skills        Json     // Detailed skill breakdown
  learning_velocity Float    // Progress per hour
  consistency_score Float    // How consistent the learning is
  streak_count      Int      // Current learning streak
  total_study_time  Int      // Total minutes studied
  snapshot_date     DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  
  @@unique([user_id, skill_domain, snapshot_date])
  @@index([user_id])
  @@index([snapshot_date])
}

model LearningGoal {
  id              String   @id @default(uuid())
  user_id         String
  goal_type       String   // 'skill_level', 'words_learned', 'study_time'
  target_value    Float
  current_value   Float    @default(0.0)
  target_date     DateTime
  created_date    DateTime @default(now())
  completed_date  DateTime?
  is_active       Boolean  @default(true)
  priority        Int      @default(1) // 1-5 priority level
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([target_date])
  @@index([is_active])
}

model Achievement {
  id              String   @id @default(uuid())
  user_id         String
  achievement_type String  // 'streak', 'milestone', 'speed', 'accuracy'
  title           String
  description     String
  icon_url        String?
  points_awarded  Int      @default(0)
  unlocked_date   DateTime @default(now())
  rarity_level    String   @default("common") // common, rare, epic, legendary
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([achievement_type])
  @@index([unlocked_date])
}

model LiveSession {
  id                String   @id @default(uuid())
  user_id           String
  session_start     DateTime @default(now())
  session_end       DateTime?
  is_active         Boolean  @default(true)
  activity_type     String   // 'vocabulary', 'reading', 'practice'
  words_reviewed    Int      @default(0)
  correct_answers   Int      @default(0)
  total_answers     Int      @default(0)
  current_streak    Int      @default(0)
  focus_score       Float    @default(1.0) // 0.0-1.0
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([is_active])
  @@index([session_start])
}
```

## Implementation Plan

### Phase 1: Event Tracking Infrastructure (Week 1-2)

#### 1.1 Real-time Event System
```typescript
// src/backend/services/event-tracking.service.ts
export interface EventTrackingService {
  trackEvent(event: LearningEvent): Promise<void>;
  getEventStream(userId: string): AsyncIterable<LearningEvent>;
  calculateProgressDelta(event: LearningEvent): Promise<number>;
  updateRealTimeMetrics(userId: string, event: LearningEvent): Promise<void>;
}

interface LearningEvent {
  userId: string;
  eventType: EventType;
  eventData: Record<string, any>;
  skillDomain: string;
  sessionId: string;
  timestamp: Date;
}

enum EventType {
  WORD_LEARNED = 'word_learned',
  EXERCISE_COMPLETED = 'exercise_completed',
  MILESTONE_REACHED = 'milestone_reached',
  SESSION_STARTED = 'session_started',
  SESSION_ENDED = 'session_ended',
  STREAK_UPDATED = 'streak_updated',
  GOAL_ACHIEVED = 'goal_achieved'
}
```

#### 1.2 Event Processing Pipeline
```typescript
class EventProcessor {
  private eventQueue: Queue<LearningEvent> = new Queue();
  private processors: Map<EventType, EventHandler> = new Map();
  
  constructor() {
    this.setupEventHandlers();
    this.startProcessing();
  }
  
  async processEvent(event: LearningEvent): Promise<void> {
    // Add to queue for async processing
    this.eventQueue.enqueue(event);
    
    // Process critical events immediately
    if (this.isCriticalEvent(event)) {
      await this.processImmediately(event);
    }
  }
  
  private async processImmediately(event: LearningEvent): Promise<void> {
    const handler = this.processors.get(event.eventType);
    if (handler) {
      await handler.process(event);
    }
    
    // Update real-time metrics
    await this.updateLiveMetrics(event);
    
    // Check for achievements
    await this.checkAchievements(event);
    
    // Notify connected clients
    await this.notifyClients(event);
  }
}
```

#### 1.3 WebSocket Integration
```typescript
// src/backend/services/websocket.service.ts
export interface WebSocketService {
  subscribeToProgress(userId: string, socket: WebSocket): void;
  broadcastProgressUpdate(userId: string, update: ProgressUpdate): Promise<void>;
  sendAchievementNotification(userId: string, achievement: Achievement): Promise<void>;
}

class ProgressWebSocketHandler {
  private connections: Map<string, Set<WebSocket>> = new Map();
  
  subscribeToProgress(userId: string, socket: WebSocket): void {
    if (!this.connections.has(userId)) {
      this.connections.set(userId, new Set());
    }
    this.connections.get(userId)!.add(socket);
    
    // Send current progress state
    this.sendCurrentProgress(userId, socket);
  }
  
  async broadcastProgressUpdate(
    userId: string, 
    update: ProgressUpdate
  ): Promise<void> {
    const userConnections = this.connections.get(userId);
    if (userConnections) {
      const message = JSON.stringify({
        type: 'progress_update',
        data: update
      });
      
      userConnections.forEach(socket => {
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(message);
        }
      });
    }
  }
}
```

### Phase 2: Progress Analytics Engine (Week 3-4)

#### 2.1 Multi-dimensional Progress Calculation
```typescript
// src/backend/services/progress-analytics.service.ts
export interface ProgressAnalyticsService {
  calculateOverallProgress(userId: string): Promise<OverallProgress>;
  getSkillBreakdown(userId: string): Promise<SkillBreakdown>;
  calculateLearningVelocity(userId: string, timeframe: TimeFrame): Promise<number>;
  generateProgressReport(userId: string, period: TimePeriod): Promise<ProgressReport>;
  predictProgressTrend(userId: string, days: number): Promise<ProgressTrend>;
}

interface OverallProgress {
  userId: string;
  overallPercentage: number; // 0-100
  skillDomains: {
    vocabulary: SkillProgress;
    grammar: SkillProgress;
    reading: SkillProgress;
    listening: SkillProgress;
  };
  learningVelocity: number; // progress per hour
  consistencyScore: number; // 0-1
  currentStreak: number;
  totalStudyTime: number; // minutes
}

interface SkillProgress {
  currentLevel: number; // 0-1
  targetLevel: number; // 0-1
  progressToTarget: number; // 0-1
  subSkills: SubSkillProgress[];
  recentTrend: 'improving' | 'stable' | 'declining';
}
```

#### 2.2 Advanced Analytics Algorithms
```typescript
class ProgressCalculator {
  calculateWeightedProgress(
    events: ProgressEvent[],
    weights: SkillWeights
  ): number {
    let totalProgress = 0;
    let totalWeight = 0;
    
    for (const [skill, weight] of Object.entries(weights)) {
      const skillEvents = events.filter(e => e.skill_domain === skill);
      const skillProgress = this.calculateSkillProgress(skillEvents);
      
      totalProgress += skillProgress * weight;
      totalWeight += weight;
    }
    
    return totalWeight > 0 ? totalProgress / totalWeight : 0;
  }
  
  calculateLearningVelocity(
    progressSnapshots: ProgressSnapshot[],
    timeframe: number // hours
  ): number {
    if (progressSnapshots.length < 2) return 0;
    
    const sorted = progressSnapshots.sort(
      (a, b) => a.snapshot_date.getTime() - b.snapshot_date.getTime()
    );
    
    const startProgress = sorted[0].overall_progress;
    const endProgress = sorted[sorted.length - 1].overall_progress;
    const progressDelta = endProgress - startProgress;
    
    return progressDelta / timeframe;
  }
  
  calculateConsistencyScore(events: ProgressEvent[]): number {
    // Calculate coefficient of variation for daily progress
    const dailyProgress = this.groupEventsByDay(events);
    const progressValues = Object.values(dailyProgress);
    
    if (progressValues.length < 2) return 1.0;
    
    const mean = progressValues.reduce((a, b) => a + b, 0) / progressValues.length;
    const variance = progressValues.reduce(
      (acc, val) => acc + Math.pow(val - mean, 2), 
      0
    ) / progressValues.length;
    
    const standardDeviation = Math.sqrt(variance);
    const coefficientOfVariation = standardDeviation / mean;
    
    // Convert to consistency score (lower variation = higher consistency)
    return Math.max(0, 1 - coefficientOfVariation);
  }
}
```

### Phase 3: Live Dashboard Implementation (Week 5-6)

#### 3.1 Real-time Progress Components
```typescript
// src/components/ui/live-progress-dashboard.tsx
export function LiveProgressDashboard({ userId }: { userId: string }) {
  const { progress, loading, error } = useLiveProgress(userId);
  const { achievements } = useAchievements(userId);
  const { currentSession } = useLiveSession(userId);
  
  return (
    <div className="live-progress-dashboard">
      <div className="dashboard-header">
        <h2>Live Progress</h2>
        <LiveSessionIndicator session={currentSession} />
      </div>
      
      <div className="progress-grid">
        <OverallProgressCard progress={progress.overall} />
        <SkillBreakdownChart skills={progress.skillDomains} />
        <LearningVelocityGauge velocity={progress.learningVelocity} />
        <StreakCounter streak={progress.currentStreak} />
      </div>
      
      <div className="recent-achievements">
        <h3>Recent Achievements</h3>
        <AchievementsList achievements={achievements.slice(0, 5)} />
      </div>
      
      <div className="live-metrics">
        <LiveMetricsPanel userId={userId} />
      </div>
    </div>
  );
}
```

#### 3.2 Real-time Data Hooks
```typescript
// src/hooks/use-live-progress.ts
export function useLiveProgress(userId: string) {
  const [progress, setProgress] = useState<OverallProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [socket, setSocket] = useState<WebSocket | null>(null);
  
  useEffect(() => {
    // Establish WebSocket connection
    const ws = new WebSocket(`${WS_URL}/progress/${userId}`);
    
    ws.onopen = () => {
      setSocket(ws);
      setLoading(false);
    };
    
    ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      
      switch (message.type) {
        case 'progress_update':
          setProgress(prev => ({
            ...prev,
            ...message.data
          }));
          break;
        case 'achievement_unlocked':
          // Handle achievement notification
          showAchievementNotification(message.data);
          break;
      }
    };
    
    ws.onerror = (error) => {
      setError(new Error('WebSocket connection failed'));
    };
    
    return () => {
      ws.close();
    };
  }, [userId]);
  
  const updateProgress = useCallback((update: Partial<OverallProgress>) => {
    setProgress(prev => prev ? { ...prev, ...update } : null);
  }, []);
  
  return {
    progress,
    loading,
    error,
    updateProgress,
    isConnected: socket?.readyState === WebSocket.OPEN
  };
}
```

### Phase 4: Achievement System Integration (Week 7-8)

#### 4.1 Achievement Detection Engine
```typescript
// src/backend/services/achievement.service.ts
export interface AchievementService {
  checkAchievements(userId: string, event: LearningEvent): Promise<Achievement[]>;
  unlockAchievement(userId: string, achievementType: string): Promise<Achievement>;
  getUserAchievements(userId: string): Promise<Achievement[]>;
  calculateAchievementProgress(userId: string): Promise<AchievementProgress[]>;
}

class AchievementDetector {
  private rules: Map<string, AchievementRule> = new Map();
  
  constructor() {
    this.setupAchievementRules();
  }
  
  async checkForAchievements(
    userId: string, 
    event: LearningEvent
  ): Promise<Achievement[]> {
    const newAchievements: Achievement[] = [];
    
    for (const [type, rule] of this.rules) {
      if (await rule.isTriggered(userId, event)) {
        const achievement = await this.unlockAchievement(userId, type);
        newAchievements.push(achievement);
      }
    }
    
    return newAchievements;
  }
  
  private setupAchievementRules(): void {
    this.rules.set('first_word', new FirstWordRule());
    this.rules.set('streak_7', new StreakRule(7));
    this.rules.set('streak_30', new StreakRule(30));
    this.rules.set('speed_demon', new SpeedRule());
    this.rules.set('perfectionist', new AccuracyRule());
    this.rules.set('vocabulary_master', new VocabularyMilestoneRule());
  }
}

interface AchievementRule {
  isTriggered(userId: string, event: LearningEvent): Promise<boolean>;
}

class StreakRule implements AchievementRule {
  constructor(private targetStreak: number) {}
  
  async isTriggered(userId: string, event: LearningEvent): Promise<boolean> {
    if (event.eventType !== EventType.STREAK_UPDATED) return false;
    
    const currentStreak = event.eventData.streak as number;
    return currentStreak === this.targetStreak;
  }
}
```

## Performance Optimization

### Caching Strategy
```typescript
class ProgressCache {
  private cache: Map<string, CachedProgress> = new Map();
  private readonly TTL = 60000; // 1 minute
  
  async getProgress(userId: string): Promise<OverallProgress | null> {
    const cached = this.cache.get(userId);
    
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached.progress;
    }
    
    return null;
  }
  
  setProgress(userId: string, progress: OverallProgress): void {
    this.cache.set(userId, {
      progress,
      timestamp: Date.now()
    });
  }
  
  invalidate(userId: string): void {
    this.cache.delete(userId);
  }
}
```

### Database Optimization
```typescript
// Efficient progress calculation using aggregation
class OptimizedProgressCalculator {
  async calculateProgressEfficiently(userId: string): Promise<OverallProgress> {
    // Use database aggregation instead of fetching all events
    const aggregatedData = await this.prisma.$queryRaw`
      SELECT 
        skill_domain,
        COUNT(*) as event_count,
        SUM(progress_delta) as total_progress,
        AVG(progress_delta) as avg_progress,
        MAX(timestamp) as last_activity
      FROM ProgressEvent 
      WHERE user_id = ${userId} 
        AND timestamp > NOW() - INTERVAL '30 days'
      GROUP BY skill_domain
    `;
    
    return this.buildProgressFromAggregation(aggregatedData);
  }
}
```

## Success Criteria

### Technical Metrics
- Real-time update latency < 100ms
- 99.9% WebSocket connection uptime
- Progress calculation accuracy > 95%
- Dashboard load time < 2 seconds

### User Experience Metrics
- User engagement increase by 30%
- Session completion rate improvement by 25%
- User satisfaction score > 4.5/5
- Achievement unlock rate > 80%

## Timeline

- **Week 1-2**: Event tracking infrastructure and WebSocket setup
- **Week 3-4**: Progress analytics engine and calculation algorithms
- **Week 5-6**: Live dashboard implementation and real-time components
- **Week 7-8**: Achievement system integration and optimization
- **Week 9**: Testing, performance optimization, and bug fixes
- **Week 10**: Deployment and monitoring setup
