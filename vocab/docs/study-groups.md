# Study Groups Development Plan

## Overview
Implement a comprehensive study groups system that enables collaborative learning, peer support, and group-based challenges in the vocabulary learning application.

## Technical Architecture

### Database Schema Extensions

#### Study Group Models
```prisma
model StudyGroup {
  id              String            @id @default(uuid())
  name            String
  description     String?
  isPublic        Boolean           @default(true)
  maxMembers      Int               @default(50)
  language        Language          @default(EN)
  level           Difficulty        @default(BEGINNER)
  createdBy       String
  inviteCode      String            @unique
  settings        Json?             // Group settings and preferences
  isActive        Boolean           @default(true)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  
  creator         User              @relation("GroupCreator", fields: [createdBy], references: [id])
  members         StudyGroupMember[]
  challenges      GroupChallenge[]
  sessions        StudySession[]
  messages        GroupMessage[]
  
  @@index([isPublic, isActive])
  @@index([language, level])
  @@map("study_groups")
}

model StudyGroupMember {
  id          String          @id @default(uuid())
  groupId     String
  userId      String
  role        GroupRole       @default(MEMBER)
  joinedAt    DateTime        @default(now())
  lastActive  DateTime        @default(now())
  isActive    Boolean         @default(true)
  
  group       StudyGroup      @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user        User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([groupId, userId])
  @@index([groupId])
  @@index([userId])
  @@map("study_group_members")
}

model StudySession {
  id              String              @id @default(uuid())
  groupId         String
  title           String
  description     String?
  scheduledAt     DateTime
  duration        Int                 // in minutes
  type            SessionType
  status          SessionStatus       @default(SCHEDULED)
  maxParticipants Int?
  createdBy       String
  settings        Json?               // Session-specific settings
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  
  group           StudyGroup          @relation(fields: [groupId], references: [id], onDelete: Cascade)
  creator         User                @relation(fields: [createdBy], references: [id])
  participants    SessionParticipant[]
  activities      SessionActivity[]
  
  @@index([groupId, scheduledAt])
  @@index([status, scheduledAt])
  @@map("study_sessions")
}

model SessionParticipant {
  id          String       @id @default(uuid())
  sessionId   String
  userId      String
  joinedAt    DateTime?
  leftAt      DateTime?
  status      ParticipantStatus @default(REGISTERED)
  
  session     StudySession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([sessionId, userId])
  @@index([sessionId])
  @@index([userId])
  @@map("session_participants")
}

model GroupChallenge {
  id          String            @id @default(uuid())
  groupId     String
  title       String
  description String?
  type        ChallengeType
  startDate   DateTime
  endDate     DateTime
  settings    Json              // Challenge-specific settings
  createdBy   String
  isActive    Boolean           @default(true)
  createdAt   DateTime          @default(now())
  
  group       StudyGroup        @relation(fields: [groupId], references: [id], onDelete: Cascade)
  creator     User              @relation(fields: [createdBy], references: [id])
  participants ChallengeParticipant[]
  
  @@index([groupId, isActive])
  @@index([startDate, endDate])
  @@map("group_challenges")
}

model ChallengeParticipant {
  id            String         @id @default(uuid())
  challengeId   String
  userId        String
  score         Int            @default(0)
  progress      Json?          // Progress tracking
  completedAt   DateTime?
  rank          Int?
  
  challenge     GroupChallenge @relation(fields: [challengeId], references: [id], onDelete: Cascade)
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([challengeId, userId])
  @@index([challengeId, score])
  @@map("challenge_participants")
}

model GroupMessage {
  id        String     @id @default(uuid())
  groupId   String
  userId    String
  content   String
  type      MessageType @default(TEXT)
  metadata  Json?      // For attachments, replies, etc.
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  
  group     StudyGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([groupId, createdAt])
  @@map("group_messages")
}

enum GroupRole {
  OWNER
  MODERATOR
  MEMBER
}

enum SessionType {
  VOCABULARY_REVIEW
  PARAGRAPH_PRACTICE
  DISCUSSION
  QUIZ_COMPETITION
  PEER_TEACHING
}

enum SessionStatus {
  SCHEDULED
  ACTIVE
  COMPLETED
  CANCELLED
}

enum ParticipantStatus {
  REGISTERED
  JOINED
  LEFT
  ABSENT
}

enum ChallengeType {
  WORD_COUNT
  STREAK_CHALLENGE
  ACCURACY_CHALLENGE
  TIME_CHALLENGE
  COLLABORATIVE_GOAL
}

enum MessageType {
  TEXT
  SYSTEM
  CHALLENGE_UPDATE
  SESSION_REMINDER
}
```

#### User Model Extensions
```prisma
model User {
  // ... existing fields
  createdGroups       StudyGroup[]         @relation("GroupCreator")
  groupMemberships    StudyGroupMember[]
  createdSessions     StudySession[]
  sessionParticipations SessionParticipant[]
  createdChallenges   GroupChallenge[]
  challengeParticipations ChallengeParticipant[]
  groupMessages       GroupMessage[]
}
```

### Backend Implementation

#### Services

**Study Group Service** (`src/backend/services/study-group.service.ts`)
```typescript
export interface StudyGroupService {
  createGroup(userId: string, groupData: CreateGroupDto): Promise<StudyGroup>;
  joinGroup(userId: string, inviteCode: string): Promise<StudyGroupMember>;
  leaveGroup(userId: string, groupId: string): Promise<void>;
  getGroupsByUser(userId: string): Promise<StudyGroup[]>;
  getPublicGroups(filters: GroupFilters): Promise<StudyGroup[]>;
  updateGroupSettings(userId: string, groupId: string, settings: UpdateGroupDto): Promise<StudyGroup>;
  inviteMembers(userId: string, groupId: string, inviteData: InviteDto): Promise<void>;
  removeMembers(userId: string, groupId: string, memberIds: string[]): Promise<void>;
}

export class StudyGroupServiceImpl implements StudyGroupService {
  constructor(
    private getStudyGroupRepository: () => StudyGroupRepository,
    private getStudyGroupMemberRepository: () => StudyGroupMemberRepository,
    private getNotificationService: () => NotificationService
  ) {}

  async createGroup(userId: string, groupData: CreateGroupDto): Promise<StudyGroup> {
    const inviteCode = this.generateInviteCode();
    
    const group = await this.getStudyGroupRepository().create({
      ...groupData,
      createdBy: userId,
      inviteCode,
    });

    // Add creator as owner
    await this.getStudyGroupMemberRepository().create({
      groupId: group.id,
      userId,
      role: GroupRole.OWNER,
    });

    return group;
  }

  async joinGroup(userId: string, inviteCode: string): Promise<StudyGroupMember> {
    const group = await this.getStudyGroupRepository().findByInviteCode(inviteCode);
    if (!group) {
      throw new NotFoundError('Study group not found');
    }

    if (!group.isActive) {
      throw new ValidationError('Study group is not active');
    }

    const memberCount = await this.getStudyGroupMemberRepository()
      .countActiveMembers(group.id);
    
    if (memberCount >= group.maxMembers) {
      throw new ValidationError('Study group is full');
    }

    const existingMember = await this.getStudyGroupMemberRepository()
      .findByGroupAndUser(group.id, userId);
    
    if (existingMember) {
      if (existingMember.isActive) {
        throw new ValidationError('Already a member of this group');
      } else {
        // Reactivate membership
        return await this.getStudyGroupMemberRepository()
          .update(existingMember.id, { isActive: true, joinedAt: new Date() });
      }
    }

    const member = await this.getStudyGroupMemberRepository().create({
      groupId: group.id,
      userId,
      role: GroupRole.MEMBER,
    });

    // Notify group members
    await this.getNotificationService().notifyGroupMembers(
      group.id,
      `New member joined: ${member.user.displayName || 'User'}`,
      { type: 'member_joined', userId }
    );

    return member;
  }

  async getPublicGroups(filters: GroupFilters): Promise<StudyGroup[]> {
    return await this.getStudyGroupRepository().findPublicGroups({
      language: filters.language,
      level: filters.level,
      searchTerm: filters.searchTerm,
      limit: filters.limit || 20,
      offset: filters.offset || 0,
    });
  }

  private generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }
}
```

**Study Session Service** (`src/backend/services/study-session.service.ts`)
```typescript
export interface StudySessionService {
  createSession(userId: string, sessionData: CreateSessionDto): Promise<StudySession>;
  joinSession(userId: string, sessionId: string): Promise<SessionParticipant>;
  startSession(userId: string, sessionId: string): Promise<StudySession>;
  endSession(userId: string, sessionId: string): Promise<StudySession>;
  getUpcomingSessions(userId: string): Promise<StudySession[]>;
  getSessionHistory(groupId: string): Promise<StudySession[]>;
}

export class StudySessionServiceImpl implements StudySessionService {
  async createSession(userId: string, sessionData: CreateSessionDto): Promise<StudySession> {
    // Verify user is group member with appropriate permissions
    const membership = await this.getStudyGroupMemberRepository()
      .findByGroupAndUser(sessionData.groupId, userId);
    
    if (!membership || !membership.isActive) {
      throw new UnauthorizedError('Not a member of this group');
    }

    if (membership.role === GroupRole.MEMBER && !this.canMemberCreateSessions(sessionData.groupId)) {
      throw new UnauthorizedError('Insufficient permissions to create sessions');
    }

    const session = await this.getStudySessionRepository().create({
      ...sessionData,
      createdBy: userId,
    });

    // Auto-register creator
    await this.getSessionParticipantRepository().create({
      sessionId: session.id,
      userId,
      status: ParticipantStatus.REGISTERED,
    });

    // Notify group members
    await this.getNotificationService().notifyGroupMembers(
      sessionData.groupId,
      `New study session scheduled: ${session.title}`,
      { type: 'session_created', sessionId: session.id }
    );

    return session;
  }

  async startSession(userId: string, sessionId: string): Promise<StudySession> {
    const session = await this.getStudySessionRepository().findById(sessionId);
    if (!session) {
      throw new NotFoundError('Session not found');
    }

    if (session.createdBy !== userId) {
      throw new UnauthorizedError('Only session creator can start the session');
    }

    if (session.status !== SessionStatus.SCHEDULED) {
      throw new ValidationError('Session cannot be started');
    }

    const updatedSession = await this.getStudySessionRepository().update(sessionId, {
      status: SessionStatus.ACTIVE,
    });

    // Update participant statuses
    await this.getSessionParticipantRepository()
      .updateRegisteredToJoined(sessionId);

    return updatedSession;
  }
}
```

### Frontend Implementation

#### Components

**Study Group Card Component** (`src/components/ui/study-group-card.tsx`)
```typescript
interface StudyGroupCardProps {
  group: StudyGroup;
  userMembership?: StudyGroupMember;
  onJoin?: (groupId: string) => void;
  onLeave?: (groupId: string) => void;
}

export function StudyGroupCard({ group, userMembership, onJoin, onLeave }: StudyGroupCardProps) {
  const memberCount = group.members?.filter(m => m.isActive).length || 0;
  const isMember = !!userMembership?.isActive;

  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{group.name}</h3>
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">{group.description}</p>
          
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <Users className="w-4 h-4" />
              <span>{memberCount}/{group.maxMembers}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Globe className="w-4 h-4" />
              <span>{group.language}</span>
            </div>
            <div className="flex items-center space-x-1">
              <BarChart className="w-4 h-4" />
              <span>{group.level}</span>
            </div>
          </div>
        </div>
        
        <div className="flex flex-col items-end space-y-2">
          {group.isPublic ? (
            <Badge variant="secondary">Public</Badge>
          ) : (
            <Badge variant="outline">Private</Badge>
          )}
          
          {isMember ? (
            <div className="flex space-x-2">
              <Button size="sm" variant="outline" onClick={() => onLeave?.(group.id)}>
                Leave
              </Button>
              <Button size="sm" asChild>
                <Link href={`/study-groups/${group.id}`}>
                  View
                </Link>
              </Button>
            </div>
          ) : (
            <Button 
              size="sm" 
              onClick={() => onJoin?.(group.id)}
              disabled={memberCount >= group.maxMembers}
            >
              {memberCount >= group.maxMembers ? 'Full' : 'Join'}
            </Button>
          )}
        </div>
      </div>
      
      {/* Recent activity or upcoming sessions */}
      <div className="border-t pt-3">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Created {formatDistanceToNow(new Date(group.createdAt))} ago</span>
          {group.sessions && group.sessions.length > 0 && (
            <span>Next session: {format(new Date(group.sessions[0].scheduledAt), 'MMM d, HH:mm')}</span>
          )}
        </div>
      </div>
    </div>
  );
}
```

**Study Session Component** (`src/components/ui/study-session.tsx`)
```typescript
interface StudySessionProps {
  session: StudySession;
  userParticipation?: SessionParticipant;
  onJoin?: (sessionId: string) => void;
  onLeave?: (sessionId: string) => void;
  onStart?: (sessionId: string) => void;
}

export function StudySessionComponent({ 
  session, 
  userParticipation, 
  onJoin, 
  onLeave, 
  onStart 
}: StudySessionProps) {
  const isParticipant = !!userParticipation;
  const canStart = session.createdBy === userParticipation?.userId && session.status === SessionStatus.SCHEDULED;
  const participantCount = session.participants?.length || 0;

  const getStatusColor = (status: SessionStatus) => {
    switch (status) {
      case SessionStatus.SCHEDULED: return 'bg-blue-100 text-blue-800';
      case SessionStatus.ACTIVE: return 'bg-green-100 text-green-800';
      case SessionStatus.COMPLETED: return 'bg-gray-100 text-gray-800';
      case SessionStatus.CANCELLED: return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <h4 className="font-medium text-gray-900">{session.title}</h4>
            <Badge className={getStatusColor(session.status)}>
              {session.status}
            </Badge>
          </div>
          
          {session.description && (
            <p className="text-sm text-gray-600 mb-2">{session.description}</p>
          )}
          
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>{format(new Date(session.scheduledAt), 'MMM d, yyyy HH:mm')}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{session.duration} min</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users className="w-4 h-4" />
              <span>{participantCount}{session.maxParticipants ? `/${session.maxParticipants}` : ''}</span>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {canStart && (
            <Button size="sm" onClick={() => onStart?.(session.id)}>
              Start
            </Button>
          )}
          
          {isParticipant ? (
            <Button size="sm" variant="outline" onClick={() => onLeave?.(session.id)}>
              Leave
            </Button>
          ) : (
            <Button 
              size="sm" 
              onClick={() => onJoin?.(session.id)}
              disabled={session.maxParticipants ? participantCount >= session.maxParticipants : false}
            >
              Join
            </Button>
          )}
        </div>
      </div>
      
      {/* Participant avatars */}
      {session.participants && session.participants.length > 0 && (
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">Participants:</span>
          <div className="flex -space-x-2">
            {session.participants.slice(0, 5).map(participant => (
              <UserAvatar 
                key={participant.id} 
                userId={participant.userId} 
                size="xs"
                className="border-2 border-white"
              />
            ))}
            {session.participants.length > 5 && (
              <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs text-gray-600 border-2 border-white">
                +{session.participants.length - 5}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
```

#### Hooks

**Study Groups Hook** (`src/hooks/use-study-groups.ts`)
```typescript
export function useStudyGroups() {
  const [userGroups, setUserGroups] = useState<StudyGroup[]>([]);
  const [publicGroups, setPublicGroups] = useState<StudyGroup[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchUserGroups = useCallback(async () => {
    setLoading(true);
    try {
      const groups = await getUserGroupsApi();
      setUserGroups(groups);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch user groups'));
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchPublicGroups = useCallback(async (filters?: GroupFilters) => {
    setLoading(true);
    try {
      const groups = await getPublicGroupsApi(filters);
      setPublicGroups(groups);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch public groups'));
    } finally {
      setLoading(false);
    }
  }, []);

  const joinGroup = useCallback(async (inviteCode: string) => {
    try {
      await joinGroupApi(inviteCode);
      await fetchUserGroups();
      toast.success('Successfully joined the study group!');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to join group');
      setError(error);
      toast.error(error.message);
    }
  }, [fetchUserGroups]);

  const leaveGroup = useCallback(async (groupId: string) => {
    try {
      await leaveGroupApi(groupId);
      setUserGroups(prev => prev.filter(g => g.id !== groupId));
      toast.success('Left the study group');
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to leave group');
      setError(error);
      toast.error(error.message);
    }
  }, []);

  const createGroup = useCallback(async (groupData: CreateGroupDto) => {
    try {
      const newGroup = await createGroupApi(groupData);
      setUserGroups(prev => [newGroup, ...prev]);
      toast.success('Study group created successfully!');
      return newGroup;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create group');
      setError(error);
      toast.error(error.message);
      throw error;
    }
  }, []);

  return {
    userGroups,
    publicGroups,
    loading,
    error,
    fetchUserGroups,
    fetchPublicGroups,
    joinGroup,
    leaveGroup,
    createGroup
  };
}
```

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure
- Database schema implementation
- Basic group management service
- Group creation and membership

### Phase 2 (Weeks 3-4): Study Sessions
- Session scheduling system
- Participant management
- Session lifecycle management

### Phase 3 (Weeks 5-6): Frontend Components
- Group discovery and joining
- Session management interface
- Group communication features

### Phase 4 (Weeks 7-8): Advanced Features
- Group challenges
- Real-time messaging
- Analytics and insights

## Success Metrics
- Group creation and participation rates
- Session attendance and completion
- User engagement within groups
- Collaborative learning outcomes
- Community growth and retention

## Future Enhancements
- Video/audio session support
- Advanced group analytics
- Mentorship programs
- Cross-group competitions
- AI-powered group recommendations
