services:
    postgresql:
        image: postgres:latest
        environment:
            - POSTGRES_DB=vocab
            - POSTGRES_USER=postgres
            - POSTGRES_PASSWORD=postgres
        ports:
            - '5432:5432'
        volumes:
            - postgresql_data:/var/lib/postgresql/data
        networks:
            - vocab-network

networks:
    vocab-network:
        driver: bridge

volumes:
    postgresql_data:
