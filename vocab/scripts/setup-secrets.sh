#!/bin/bash

# Exit on error
set -e

# Check if gh CLI is installed
if ! command -v gh &> /dev/null; then
    echo "GitHub CLI (gh) is not installed. Please install it first:"
    echo "https://cli.github.com/manual/installation"
    exit 1
fi

# Check if user is authenticated with GitHub
if ! gh auth status &> /dev/null; then
    echo "Please authenticate with GitHub first:"
    echo "gh auth login"
    exit 1
fi

# Get repository name
REPO=$(gh repo view --json name -q .name)
OWNER=$(gh repo view --json owner -q .owner.login)

echo "Setting up secrets for $OWNER/$REPO"

# Frontend secrets
echo "Setting up frontend secrets..."
read -p "Enter Vercel Access Token: " VERCEL_ACCESS_TOKEN

# Set secrets
echo "Setting secrets in GitHub..."

# Frontend secrets
gh secret set VERCEL_ACCESS_TOKEN -b "$VERCEL_ACCESS_TOKEN"

echo "✅ Secrets have been set up successfully!"
echo "You can now push your code to trigger the CI/CD pipelines." 