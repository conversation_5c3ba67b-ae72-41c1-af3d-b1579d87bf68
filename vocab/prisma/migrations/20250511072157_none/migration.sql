-- CreateTable
CREATE TABLE "Test" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "completed" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Test_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TestResult" (
    "id" TEXT NOT NULL,
    "test_id" TEXT NOT NULL,
    "score" DOUBLE PRECISION NOT NULL,
    "total_questions" INTEGER NOT NULL,
    "correct_answers" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TestResult_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TestDetail" (
    "id" TEXT NOT NULL,
    "test_result_id" TEXT NOT NULL,
    "question_id" TEXT NOT NULL,
    "correct" BOOLEAN NOT NULL,
    "correct_answer" TEXT NOT NULL,
    "selected_answer" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TestDetail_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Test_user_id_idx" ON "Test"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "TestResult_test_id_key" ON "TestResult"("test_id");

-- CreateIndex
CREATE INDEX "TestDetail_test_result_id_idx" ON "TestDetail"("test_result_id");

-- AddForeignKey
ALTER TABLE "Test" ADD CONSTRAINT "Test_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TestResult" ADD CONSTRAINT "TestResult_test_id_fkey" FOREIGN KEY ("test_id") REFERENCES "Test"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TestDetail" ADD CONSTRAINT "TestDetail_test_result_id_fkey" FOREIGN KEY ("test_result_id") REFERENCES "TestResult"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
