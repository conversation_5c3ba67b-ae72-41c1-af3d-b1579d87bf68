'use client';

import { useCollections } from './use-collections';
import { WordDetail } from '@/models';
import { useCallback } from 'react';

// Hook for general word operations
export function useWords() {
	const {
		searchWords,
		fetchWord,
		getWordById,
		bulkDeleteWords,
		addTermToCollection,
		addWordsToCollection,
		removeWordsFromCollection,
		fetchWordsByCollection,
		getWordsToReviewWords,
		loading,
		error,
	} = useCollections();

	return {
		searchWords,
		fetchWord,
		getWordById,
		bulkDeleteWords,
		addTermToCollection,
		addWordsToCollection,
		removeWordsFromCollection,
		fetchWordsByCollection,
		getWordsToReviewWords,
		loading,
		error,
	};
}

// Hook for single word operations
export function useWord() {
	const { fetchWord, getWordById, loading, error } = useCollections();

	const getWord = useCallback(
		async (id: string): Promise<WordDetail | null> => {
			return await fetchWord(id);
		},
		[fetchWord]
	);

	return {
		getWord,
		getWordById,
		loading,
		error,
	};
}

// Hook for words context (alias for useWords for backward compatibility)
export function useWordsContext() {
	return useWords();
}

// Hook for current collection words
export function useCurrentCollectionWords() {
	const {
		currentCollectionWords,
		fetchCurrentCollectionWords,
		getCurrentCollectionWordsToReview,
		addTermToCurrentCollection,
		addWordsToCurrentCollection,
		removeWordsFromCurrentCollection,
		bulkDeleteWordsFromCurrentCollection,
		loading,
		error,
	} = useCollections();

	return {
		currentCollectionWords,
		fetchCurrentCollectionWords,
		getCurrentCollectionWordsToReview,
		addTermToCurrentCollection,
		addWordsToCurrentCollection,
		removeWordsFromCurrentCollection,
		bulkDeleteWordsFromCurrentCollection,
		loading,
		error,
	};
}
