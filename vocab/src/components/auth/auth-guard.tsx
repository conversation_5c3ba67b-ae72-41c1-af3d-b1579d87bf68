'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { LoadingSpinner } from '@/components/ui';

interface AuthGuardProps {
	children: React.ReactNode;
	fallbackPath?: string;
}

export function AuthGuard({ children, fallbackPath = '/login' }: AuthGuardProps) {
	const { user, isLoading, getUser } = useAuth();
	const router = useRouter();
	const [isInitialized, setIsInitialized] = useState(false);
	const hasAttemptedAuth = useRef(false);

	useEffect(() => {
		const initAuth = async () => {
			// Only attempt auth once and when not already loading
			if (!hasAttemptedAuth.current && !user && !isLoading) {
				hasAttemptedAuth.current = true;
				await getUser();
			}
			setIsInitialized(true);
		};

		initAuth();
	}, [user, isLoading]); // Removed getUser from dependencies

	useEffect(() => {
		if (isInitialized && !isLoading && !user) {
			router.push(fallbackPath);
		}
	}, [isInitialized, isLoading, user, router, fallbackPath]);

	if (!isInitialized || isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		);
	}

	if (!user) {
		return null; // Will redirect in useEffect
	}

	return <>{children}</>;
}
