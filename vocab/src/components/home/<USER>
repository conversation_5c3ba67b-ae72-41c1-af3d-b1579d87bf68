'use client';

import { Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface FeatureCardProps {
	icon: LucideIcon;
	titleKey: string;
	descriptionKey: string;
	ariaLabel?: string;
}

export function FeatureCard({ icon: Icon, titleKey, descriptionKey, ariaLabel }: FeatureCardProps) {
	const { t } = useTranslation();

	return (
		<motion.div
			whileHover={{ scale: 1.02 }}
			className="p-4 bg-card/80 backdrop-blur-md rounded-2xl border border-primary/20 shadow-xl hover:shadow-2xl transition-all duration-300"
			role="article"
			aria-label={ariaLabel || t(titleKey)}
		>
			<h3 className="font-semibold text-lg flex items-center">
				<Icon className="size-5 text-primary mr-2" aria-hidden="true" />
				<Translate text={titleKey} />
			</h3>
			<p className="text-sm text-muted-foreground mt-2">
				<Translate text={descriptionKey} />
			</p>
		</motion.div>
	);
}
