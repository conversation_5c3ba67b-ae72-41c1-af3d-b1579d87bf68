'use client';

import { useForm<PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '@/lib/form-error-handler';
import { useErrorHandler } from '@/contexts/error-context';
import { ErrorDisplay } from '@/components/ui/error-display';
import { DataFallback } from '@/components/fallback';
import { useRetry } from '@/lib/retry';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, FormProvider, FieldValues, SubmitHandler } from 'react-hook-form';
import { ZodSchema } from 'zod';
import { ReactNode, useCallback, useState } from 'react';
import { Button } from './button';

// ============================================================================
// TYPES
// ============================================================================

interface EnhancedFormProps<T extends FieldValues> {
	schema: ZodSchema<T>;
	onSubmit: (data: T) => Promise<void> | void;
	children: ReactNode;
	defaultValues?: Partial<T>;
	className?: string;
	submitButtonText?: string;
	enableRetry?: boolean;
	maxRetries?: number;
	showGlobalErrors?: boolean;
	isLoading?: boolean;
	loadingText?: string;
}

interface FormFieldWrapperProps {
	name: string;
	label?: string;
	children: ReactNode;
	required?: boolean;
	description?: string;
}

// ============================================================================
// ENHANCED FORM COMPONENT
// ============================================================================

export function EnhancedForm<T extends FieldValues>({
	schema,
	onSubmit,
	children,
	defaultValues,
	className = '',
	submitButtonText = 'Submit',
	enableRetry = true,
	maxRetries = 3,
	showGlobalErrors = true,
	isLoading = false,
	loadingText = 'Processing...',
}: EnhancedFormProps<T>) {
	const [submitError, setSubmitError] = useState<Error | null>(null);
	const { handleError } = useErrorHandler('EnhancedForm');
	const { fromReactHookForm, fromZodError, fromApiError, clearAllErrors } = useFormErrorHandler();

	// Setup form with validation
	const form = useForm<T>({
		resolver: zodResolver(schema),
		defaultValues: defaultValues as any,
		mode: 'onChange',
	});

	const {
		formState: { errors, isSubmitting },
		handleSubmit,
		reset,
	} = form;

	// Convert form errors to our error format
	const formErrors = fromReactHookForm(errors);

	// Setup retry mechanism for form submission
	const { execute: executeSubmit, isRetrying } = useRetry(
		async () => {
			const data = form.getValues();
			await onSubmit(data);
		},
		{
			enabled: enableRetry,
			maxAttempts: maxRetries,
			onRetry: (attempt) => {
				console.log(`Form submission retry attempt ${attempt}`);
			},
		}
	);

	// Handle form submission with error handling
	const handleFormSubmit: SubmitHandler<T> = useCallback(
		async (data) => {
			try {
				setSubmitError(null);
				clearAllErrors();

				if (enableRetry) {
					await executeSubmit();
				} else {
					await onSubmit(data);
				}
			} catch (error) {
				const normalizedError = error instanceof Error ? error : new Error(String(error));
				fromApiError(normalizedError);
				setSubmitError(normalizedError);
				handleError(error, 'form_submit', { formData: data });
			}
		},
		[onSubmit, executeSubmit, enableRetry, fromApiError, clearAllErrors, handleError]
	);

	// Handle form reset
	const handleReset = useCallback(() => {
		reset();
		setSubmitError(null);
		clearAllErrors();
	}, [reset, clearAllErrors]);

	const isFormLoading = isLoading || isSubmitting || isRetrying;

	return (
		<DataFallback isLoading={isFormLoading} error={null} className={className}>
			<FormProvider {...form}>
				<form
					onSubmit={handleSubmit(handleFormSubmit as any)}
					className={`space-y-6 ${className}`}
				>
					{/* Global form errors */}
					{showGlobalErrors && submitError && (
						<ErrorDisplay
							error={submitError}
							onDismiss={() => setSubmitError(null)}
							onRetry={enableRetry ? () => executeSubmit() : undefined}
							variant="inline"
							showDetails={process.env.NODE_ENV === 'development'}
						/>
					)}

					{/* Form fields */}
					<div className="space-y-4">{children}</div>

					{/* Form actions */}
					<div className="flex justify-between items-center pt-4">
						<Button
							type="button"
							variant="outline"
							onClick={handleReset}
							disabled={isFormLoading}
						>
							Reset
						</Button>

						<Button
							type="submit"
							disabled={isFormLoading || formErrors.hasErrors}
							className="min-w-[120px]"
						>
							{isFormLoading ? loadingText : submitButtonText}
						</Button>
					</div>

					{/* Form validation summary */}
					{formErrors.hasErrors && (
						<div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
							<p className="text-sm text-red-800 dark:text-red-200 font-medium mb-2">
								Please fix the following errors:
							</p>
							<ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
								{Object.entries(formErrors.fieldErrors).map(([field, message]) => (
									<li key={field}>
										• {field}: {message}
									</li>
								))}
							</ul>
						</div>
					)}
				</form>
			</FormProvider>
		</DataFallback>
	);
}

// ============================================================================
// FORM FIELD WRAPPER WITH ERROR HANDLING
// ============================================================================

export function FormFieldWrapper({
	name,
	label,
	children,
	required = false,
	description,
}: FormFieldWrapperProps) {
	const {
		formState: { errors },
	} = useForm();
	const { getFieldError, hasFieldError, fromReactHookForm } = useFormErrorHandler();

	const formErrors = fromReactHookForm(errors);
	const fieldError = getFieldError(formErrors, name);
	const hasError = hasFieldError(formErrors, name);

	return (
		<div className="space-y-2">
			{label && (
				<label
					htmlFor={name}
					className={`block text-sm font-medium ${
						hasError
							? 'text-red-700 dark:text-red-300'
							: 'text-gray-700 dark:text-gray-300'
					}`}
				>
					{label}
					{required && <span className="text-red-500 ml-1">*</span>}
				</label>
			)}

			{description && (
				<p className="text-sm text-gray-500 dark:text-gray-400">{description}</p>
			)}

			<div className={hasError ? 'ring-2 ring-red-500 rounded' : ''}>{children}</div>

			{fieldError && <p className="text-sm text-red-600 dark:text-red-400">{fieldError}</p>}
		</div>
	);
}

// ============================================================================
// FORM HOOKS
// ============================================================================

export function useEnhancedForm<T extends FieldValues>(schema: ZodSchema<T>) {
	const { handleError } = useErrorHandler('useEnhancedForm');
	const { fromZodError, validateForm } = useFormErrorHandler();

	const validateFormData = useCallback(
		async (data: T) => {
			try {
				schema.parse(data);
				return { isValid: true, errors: null };
			} catch (error) {
				if (error instanceof Error) {
					const formErrors = fromZodError(error as any);
					return { isValid: false, errors: formErrors };
				}
				handleError(error, 'form_validation');
				return { isValid: false, errors: null };
			}
		},
		[schema, fromZodError, handleError]
	);

	return {
		validateFormData,
		validateForm,
	};
}

// ============================================================================
// FORM UTILITIES
// ============================================================================

export function createFormSchema<T>(schema: ZodSchema<T>) {
	return {
		schema,
		validate: (data: unknown) => schema.parse(data),
		safeParse: (data: unknown) => schema.safeParse(data),
	};
}
