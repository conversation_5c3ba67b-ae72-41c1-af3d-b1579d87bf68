'use client';

import { cn } from '@/lib';
import React, { HTMLAttributes, forwardRef } from 'react';

// Component to hide content visually but keep it available for screen readers
type VisuallyHiddenProps = {
	children: React.ReactNode;
	className?: string;
	as?: 'span' | 'div' | 'p';
} & React.HTMLAttributes<HTMLElement>;

const VisuallyHidden = React.forwardRef<HTMLElement, VisuallyHiddenProps>(
	({ children, className, as: Component = 'span', ...props }, ref) => {
		return (
			<Component
				ref={ref as any}
				className={cn(
					'absolute w-px h-px p-0 overflow-hidden clip-[rect(0,0,0,0)] whitespace-nowrap border-0',
					className
				)}
				{...props}
			>
				{children}
			</Component>
		);
	}
);

VisuallyHidden.displayName = 'VisuallyHidden';

export { VisuallyHidden };

// Live region component for announcements to screen readers
export const LiveRegion = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
	({ className, ...props }, ref) => {
		return (
			<div
				ref={ref}
				className={cn(
					'absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0',
					className
				)}
				style={{ clip: 'rect(0 0 0 0)', clipPath: 'inset(50%)' }}
				aria-live="polite"
				aria-atomic="true"
				{...props}
			/>
		);
	}
);

LiveRegion.displayName = 'LiveRegion';
