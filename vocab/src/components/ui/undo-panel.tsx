'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Undo2, ChevronDown, Clock, X } from 'lucide-react';
import { Button } from './button';
import { Card, CardContent } from './card';
import { cn } from '@/lib';
import { UndoAction, formatUndoDescription } from '@/hooks/use-undo-actions';

// ============================================================================
// TYPES
// ============================================================================

interface UndoPanelProps {
	actions: UndoAction[];
	onUndo: (actionId: string) => Promise<boolean>;
	onDismiss: (actionId: string) => void;
	isUndoing?: boolean;
	className?: string;
}

interface UndoDropdownProps extends UndoPanelProps {
	maxVisible?: number;
}

// ============================================================================
// UNDO PANEL COMPONENT
// ============================================================================

export function UndoPanel({
	actions,
	onUndo,
	onDismiss,
	isUndoing = false,
	className,
}: UndoPanelProps) {
	if (actions.length === 0) return null;

	return (
		<Card className={cn('w-full max-w-md', className)}>
			<CardContent className="p-4">
				<div className="space-y-3">
					<div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
						<Clock className="h-4 w-4" />
						Recent Actions
					</div>

					<div className="space-y-2">
						{actions.map((action) => (
							<UndoActionItem
								key={action.id}
								action={action}
								onUndo={onUndo}
								onDismiss={onDismiss}
								isUndoing={isUndoing}
							/>
						))}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}

// ============================================================================
// UNDO DROPDOWN COMPONENT
// ============================================================================

export function UndoDropdown({
	actions,
	onUndo,
	onDismiss,
	isUndoing = false,
	maxVisible = 5,
	className,
}: UndoDropdownProps) {
	const [isOpen, setIsOpen] = useState(false);
	const latestAction = actions[0];
	const visibleActions = actions.slice(0, maxVisible);

	if (actions.length === 0) return null;

	return (
		<div className={cn('relative', className)}>
			{/* Trigger Button */}
			<Button
				variant="outline"
				size="sm"
				onClick={() => setIsOpen(!isOpen)}
				className="gap-2"
				disabled={isUndoing}
			>
				<Undo2 className="h-4 w-4" />
				{latestAction ? `Undo (${actions.length})` : 'Undo'}
				<ChevronDown
					className={cn('h-4 w-4 transition-transform', isOpen && 'rotate-180')}
				/>
			</Button>

			{/* Dropdown Panel */}
			<AnimatePresence>
				{isOpen && (
					<motion.div
						initial={{ opacity: 0, y: -10, scale: 0.95 }}
						animate={{ opacity: 1, y: 0, scale: 1 }}
						exit={{ opacity: 0, y: -10, scale: 0.95 }}
						transition={{ duration: 0.15 }}
						className="absolute top-full left-0 mt-2 z-50"
					>
						<Card className="w-80 shadow-lg border">
							<CardContent className="p-4">
								<div className="space-y-3">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
											<Clock className="h-4 w-4" />
											Recent Actions ({actions.length})
										</div>
										<Button
											variant="ghost"
											size="sm"
											onClick={() => setIsOpen(false)}
											className="h-6 w-6 p-0"
										>
											<X className="h-4 w-4" />
										</Button>
									</div>

									<div className="space-y-2 max-h-64 overflow-y-auto">
										{visibleActions.map((action) => (
											<UndoActionItem
												key={action.id}
												action={action}
												onUndo={async (actionId) => {
													const success = await onUndo(actionId);
													if (success) {
														setIsOpen(false);
													}
													return success;
												}}
												onDismiss={(actionId) => {
													onDismiss(actionId);
													if (actions.length <= 1) {
														setIsOpen(false);
													}
												}}
												isUndoing={isUndoing}
											/>
										))}
									</div>

									{actions.length > maxVisible && (
										<div className="text-xs text-muted-foreground text-center pt-2 border-t">
											+{actions.length - maxVisible} more actions
										</div>
									)}
								</div>
							</CardContent>
						</Card>
					</motion.div>
				)}
			</AnimatePresence>

			{/* Backdrop */}
			{isOpen && <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />}
		</div>
	);
}

// ============================================================================
// UNDO ACTION ITEM COMPONENT
// ============================================================================

interface UndoActionItemProps {
	action: UndoAction;
	onUndo: (actionId: string) => Promise<boolean>;
	onDismiss: (actionId: string) => void;
	isUndoing?: boolean;
}

function UndoActionItem({ action, onUndo, onDismiss, isUndoing = false }: UndoActionItemProps) {
	const [isProcessing, setIsProcessing] = useState(false);

	const handleUndo = async () => {
		if (isProcessing || isUndoing) return;

		setIsProcessing(true);
		try {
			await onUndo(action.id);
		} finally {
			setIsProcessing(false);
		}
	};

	const handleDismiss = () => {
		if (isProcessing || isUndoing) return;
		onDismiss(action.id);
	};

	return (
		<div className="flex items-center gap-3 p-2 rounded-lg border bg-background/50 hover:bg-background transition-colors">
			<div className="flex-1 min-w-0">
				<p className="text-sm font-medium text-foreground truncate">{action.description}</p>
				<p className="text-xs text-muted-foreground">{formatUndoDescription(action)}</p>
			</div>

			<div className="flex items-center gap-1 flex-shrink-0">
				<Button
					variant="ghost"
					size="sm"
					onClick={handleUndo}
					disabled={isProcessing || isUndoing}
					className="h-7 px-2 text-xs"
				>
					{isProcessing ? 'Undoing...' : 'Undo'}
				</Button>

				<Button
					variant="ghost"
					size="sm"
					onClick={handleDismiss}
					disabled={isProcessing || isUndoing}
					className="h-7 w-7 p-0"
				>
					<X className="h-3 w-3" />
				</Button>
			</div>
		</div>
	);
}

// ============================================================================
// PANEL QUICK UNDO BUTTON
// ============================================================================

interface PanelQuickUndoButtonProps {
	action: UndoAction | null;
	onUndo: (actionId: string) => Promise<boolean>;
	isUndoing?: boolean;
	className?: string;
}

export function PanelQuickUndoButton({
	action,
	onUndo,
	isUndoing = false,
	className,
}: PanelQuickUndoButtonProps) {
	const [isProcessing, setIsProcessing] = useState(false);

	if (!action) return null;

	const handleUndo = async () => {
		if (isProcessing || isUndoing) return;

		setIsProcessing(true);
		try {
			await onUndo(action.id);
		} finally {
			setIsProcessing(false);
		}
	};

	return (
		<Button
			variant="outline"
			size="sm"
			onClick={handleUndo}
			disabled={isProcessing || isUndoing}
			className={cn('gap-2', className)}
		>
			<Undo2 className="h-4 w-4" />
			{isProcessing ? 'Undoing...' : 'Undo'}
		</Button>
	);
}
