'use client';

import { Card, CardContent, CardHeader } from './card';
import { LoadingSpinner } from './loading-spinner';

interface PageSkeletonProps {
	type?: 'default' | 'dashboard' | 'form' | 'list' | 'detail';
	showHeader?: boolean;
	showNavigation?: boolean;
	className?: string;
}

export function PageSkeleton({ 
	type = 'default', 
	showHeader = true, 
	showNavigation = true,
	className = "" 
}: PageSkeletonProps) {
	if (type === 'dashboard') {
		return (
			<div className={`space-y-8 ${className}`}>
				{showNavigation && (
					<div className="flex items-center justify-between mb-6">
						<div className="flex items-center gap-2">
							<div className="h-8 w-8 bg-muted rounded animate-pulse" />
							<div className="h-6 w-32 bg-muted rounded animate-pulse" />
						</div>
						<div className="h-8 w-24 bg-muted rounded animate-pulse" />
					</div>
				)}
				
				{showHeader && (
					<div className="text-center space-y-4 mb-8">
						<div className="h-10 w-64 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-6 w-96 bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
				)}

				{/* Stats Cards */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
					{[...Array(3)].map((_, i) => (
						<Card key={i} className="animate-pulse">
							<CardHeader className="pb-3">
								<div className="flex items-center justify-between">
									<div className="h-4 w-20 bg-muted rounded animate-pulse" />
									<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								</div>
							</CardHeader>
							<CardContent>
								<div className="h-8 w-16 bg-muted rounded animate-pulse mb-2" />
								<div className="h-3 w-24 bg-muted rounded animate-pulse" />
							</CardContent>
						</Card>
					))}
				</div>

				{/* Main Content */}
				<Card className="animate-pulse">
					<CardHeader>
						<div className="h-6 w-48 bg-muted rounded animate-pulse" />
					</CardHeader>
					<CardContent>
						<div className="h-64 w-full bg-muted rounded animate-pulse" />
					</CardContent>
				</Card>
			</div>
		);
	}

	if (type === 'form') {
		return (
			<div className={`space-y-6 ${className}`}>
				{showNavigation && (
					<div className="flex items-center gap-2 mb-6">
						<div className="h-8 w-8 bg-muted rounded animate-pulse" />
						<div className="h-6 w-24 bg-muted rounded animate-pulse" />
					</div>
				)}

				{showHeader && (
					<div className="text-center space-y-4 mb-8">
						<div className="h-8 w-48 bg-muted rounded-xl animate-pulse mx-auto" />
						<div className="h-5 w-64 bg-muted rounded-lg animate-pulse mx-auto" />
					</div>
				)}

				<Card className="max-w-2xl mx-auto animate-pulse">
					<CardHeader>
						<div className="h-6 w-32 bg-muted rounded animate-pulse" />
					</CardHeader>
					<CardContent className="space-y-6">
						{[...Array(4)].map((_, i) => (
							<div key={i} className="space-y-2">
								<div className="h-4 w-24 bg-muted rounded animate-pulse" />
								<div className="h-10 w-full bg-muted rounded animate-pulse" />
							</div>
						))}
						<div className="flex justify-end gap-3 pt-4">
							<div className="h-10 w-20 bg-muted rounded animate-pulse" />
							<div className="h-10 w-24 bg-muted rounded animate-pulse" />
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (type === 'list') {
		return (
			<div className={`space-y-6 ${className}`}>
				{showNavigation && (
					<div className="flex items-center justify-between mb-6">
						<div className="flex items-center gap-2">
							<div className="h-8 w-8 bg-muted rounded animate-pulse" />
							<div className="h-6 w-32 bg-muted rounded animate-pulse" />
						</div>
						<div className="h-10 w-32 bg-muted rounded animate-pulse" />
					</div>
				)}

				{showHeader && (
					<div className="flex items-center justify-between mb-6">
						<div className="h-8 w-48 bg-muted rounded animate-pulse" />
						<div className="h-10 w-24 bg-muted rounded animate-pulse" />
					</div>
				)}

				{/* Search Bar */}
				<div className="h-10 w-full max-w-md bg-muted rounded animate-pulse mb-6" />

				{/* List Items */}
				<div className="space-y-4">
					{[...Array(5)].map((_, i) => (
						<Card key={i} className="animate-pulse">
							<CardContent className="p-4">
								<div className="flex items-center justify-between">
									<div className="flex items-center gap-3">
										<div className="h-10 w-10 bg-muted rounded-full animate-pulse" />
										<div className="space-y-2">
											<div className="h-5 w-32 bg-muted rounded animate-pulse" />
											<div className="h-3 w-24 bg-muted rounded animate-pulse" />
										</div>
									</div>
									<div className="flex gap-2">
										<div className="h-8 w-8 bg-muted rounded animate-pulse" />
										<div className="h-8 w-8 bg-muted rounded animate-pulse" />
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	if (type === 'detail') {
		return (
			<div className={`space-y-8 ${className}`}>
				{showNavigation && (
					<div className="flex items-center gap-2 mb-6">
						<div className="h-8 w-8 bg-muted rounded animate-pulse" />
						<div className="h-6 w-24 bg-muted rounded animate-pulse" />
					</div>
				)}

				{/* Header */}
				<div className="space-y-4">
					<div className="h-10 w-64 bg-muted rounded animate-pulse" />
					<div className="h-5 w-48 bg-muted rounded animate-pulse" />
				</div>

				{/* Tabs */}
				<div className="flex gap-4 border-b">
					{[...Array(4)].map((_, i) => (
						<div key={i} className="h-8 w-20 bg-muted rounded animate-pulse" />
					))}
				</div>

				{/* Content */}
				<div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
					<div className="lg:col-span-2 space-y-6">
						<Card className="animate-pulse">
							<CardHeader>
								<div className="h-6 w-32 bg-muted rounded animate-pulse" />
							</CardHeader>
							<CardContent>
								<div className="space-y-4">
									<div className="h-4 w-full bg-muted rounded animate-pulse" />
									<div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
									<div className="h-4 w-1/2 bg-muted rounded animate-pulse" />
								</div>
							</CardContent>
						</Card>
					</div>
					<div className="space-y-6">
						<Card className="animate-pulse">
							<CardHeader>
								<div className="h-5 w-24 bg-muted rounded animate-pulse" />
							</CardHeader>
							<CardContent>
								<div className="space-y-3">
									{[...Array(3)].map((_, i) => (
										<div key={i} className="flex justify-between">
											<div className="h-4 w-16 bg-muted rounded animate-pulse" />
											<div className="h-4 w-12 bg-muted rounded animate-pulse" />
										</div>
									))}
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		);
	}

	// Default skeleton
	return (
		<div className={`space-y-8 ${className}`}>
			{showNavigation && (
				<div className="flex items-center gap-2 mb-6">
					<div className="h-8 w-8 bg-muted rounded animate-pulse" />
					<div className="h-6 w-24 bg-muted rounded animate-pulse" />
				</div>
			)}

			{showHeader && (
				<div className="text-center space-y-4 mb-8">
					<div className="h-10 w-64 bg-muted rounded-xl animate-pulse mx-auto" />
					<div className="h-6 w-96 bg-muted rounded-lg animate-pulse mx-auto" />
				</div>
			)}

			<div className="space-y-6">
				{[...Array(3)].map((_, i) => (
					<Card key={i} className="animate-pulse">
						<CardHeader>
							<div className="h-6 w-48 bg-muted rounded animate-pulse" />
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								<div className="h-4 w-full bg-muted rounded animate-pulse" />
								<div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
								<div className="h-4 w-1/2 bg-muted rounded animate-pulse" />
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}

// Simple centered loading component for full page loading
export function FullPageLoading({ message }: { message?: string }) {
	return (
		<div className="min-h-screen flex items-center justify-center">
			<div className="text-center space-y-4">
				<LoadingSpinner size="lg" />
				{message && (
					<p className="text-muted-foreground">{message}</p>
				)}
			</div>
		</div>
	);
}
