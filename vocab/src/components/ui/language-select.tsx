'use client';

import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
	Translate,
} from '@/components/ui';
import { useTranslation } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { Language } from '@prisma/client';

interface LanguageSelectProps {
	value?: Language;
	onChange: (value: Language) => void;
	placeholder?: string;
}

export function LanguageSelect({ value = 'EN', onChange, placeholder }: LanguageSelectProps) {
	const { t } = useTranslation();

	return (
		<Select value={value} onValueChange={onChange}>
			<SelectTrigger className="w-[180px]">
				<SelectValue placeholder={placeholder || t('ui.select_language')} />
			</SelectTrigger>
			<SelectContent>
				<SelectItem value="EN">
					<Translate text={getTranslationKeyOfLanguage(Language.EN)} />
				</SelectItem>
				<SelectItem value="VI">
					<Translate text={getTranslationKeyOfLanguage(Language.VI)} />
				</SelectItem>
			</SelectContent>
		</Select>
	);
}
