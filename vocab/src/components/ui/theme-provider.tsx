'use client';

import { Theme, getSystemTheme, getTheme, setTheme } from '@/lib';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';

type ThemeProviderProps = {
	children: React.ReactNode;
};

type ThemeContextType = {
	theme: Theme;
	setTheme: (theme: Theme) => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: ThemeProviderProps) {
	const [theme, setThemeState] = useState<Theme>('system');

	const handleThemeChange = useCallback((newTheme: Theme) => {
		setThemeState(newTheme);
		setTheme(newTheme);
	}, []);

	const handleSystemThemeChange = useCallback(() => {
		if (theme === 'system') {
			const newTheme = getSystemTheme();
			document.documentElement.classList.toggle('dark', newTheme === 'dark');
		}
	}, [theme]);

	// Memoize the context value to prevent unnecessary re-renders
	const contextValue = useMemo(
		() => ({
			theme,
			setTheme: handleThemeChange,
		}),
		[theme, handleThemeChange]
	);

	// Load saved theme on mount
	useEffect(() => {
		const savedTheme = getTheme();
		setThemeState(savedTheme);
		setTheme(savedTheme);

		// Listen for system preference changes
		const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
		mediaQuery.addEventListener('change', handleSystemThemeChange);
		return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
	}, [theme, handleSystemThemeChange]);

	return <ThemeContext.Provider value={contextValue}>{children}</ThemeContext.Provider>;
}

export function useTheme() {
	const context = useContext(ThemeContext);
	if (context === undefined) {
		throw new Error('useTheme must be used within a ThemeProvider');
	}
	return context;
}
