import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui';
import { cn } from '@/lib';
import { Translate } from './translate';

interface TestResultsProps {
	results: {
		score: number;
		total_questions: number;
		correct_answers: number;
		details: {
			question_id: string;
			correct: boolean;
			correct_answer: string;
			selected_answer: string;
		}[];
	};
	onRetry: () => void;
}

export function TestResults({ results, onRetry }: TestResultsProps) {
	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>
						<Translate text="Test Results" />
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="text-center">
							<div className="text-4xl font-bold mb-2">{results.score}%</div>
							<div className="text-muted-foreground">
								{results.correct_answers} / {results.total_questions}{' '}
								<Translate text="correct answers" />
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			<div className="space-y-4">
				<h2 className="text-xl font-semibold">
					<Translate text="Question Review" />
				</h2>
				{results.details.map((detail, index) => (
					<Card key={detail.question_id}>
						<CardContent className="pt-6">
							<div className="space-y-2">
								<div className="flex items-center justify-between">
									<span className="font-medium">
										<Translate text="Question" /> {index + 1}
									</span>
									<span
										className={cn(
											'text-sm font-medium',
											detail.correct ? 'text-green-500' : 'text-red-500'
										)}
									>
										{detail.correct ? (
											<Translate text="Correct" />
										) : (
											<Translate text="Incorrect" />
										)}
									</span>
								</div>
								<div className="text-sm text-muted-foreground">
									<Translate text="Your answer" />: {detail.selected_answer}
								</div>
								{!detail.correct && (
									<div className="text-sm text-green-600">
										<Translate text="Correct answer" />: {detail.correct_answer}
									</div>
								)}
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			<Button onClick={onRetry} className="w-full">
				<Translate text="Try Another Test" />
			</Button>
		</div>
	);
}
