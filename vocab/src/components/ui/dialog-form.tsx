'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>Content,
	Di<PERSON>Header,
	DialogTitle,
	DialogFooter,
	Translate,
} from '@/components/ui';
import { ReactNode } from 'react';

interface DialogFormProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	titleKey: string;
	children: ReactNode;
	footer: ReactNode;
	onSubmit: (e: React.FormEvent) => void;
}

export function DialogForm({
	open,
	onOpenChange,
	titleKey,
	children,
	footer,
	onSubmit,
}: DialogFormProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>
						<Translate text={titleKey} />
					</DialogTitle>
				</DialogHeader>
				<form onSubmit={onSubmit}>
					<div className="space-y-4">{children}</div>
					<DialogFooter className="mt-6">{footer}</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
