'use client';

import { cn } from '@/lib';
import { useEffect, useState } from 'react';

interface SkipToContentProps {
	contentId: string;
	className?: string;
	label?: string;
}

/**
 * SkipToContent component - Allows keyboard users to skip navigation and go directly to main content
 * This is especially useful for screen reader users and keyboard-only users
 */
export function SkipToContent({
	contentId,
	className,
	label = 'Skip to content',
}: SkipToContentProps) {
	const [isVisible, setIsVisible] = useState(false);

	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			// Show the skip link when Tab is pressed
			if (e.key === 'Tab' && !isVisible) {
				setIsVisible(true);
			}
		};

		// Handle visibility when user navigates back to the page
		const handleFocus = () => {
			if (document.activeElement === document.body) {
				setIsVisible(true);
			}
		};

		window.addEventListener('keydown', handleKeyDown);
		window.addEventListener('focusin', handleFocus);
		return () => {
			window.removeEventListener('keydown', handleKeyDown);
			window.removeEventListener('focusin', handleFocus);
		};
	}, [isVisible]);

	const handleClick = (e: React.MouseEvent | React.KeyboardEvent) => {
		e.preventDefault();
		const contentElement = document.getElementById(contentId);
		if (contentElement) {
			// Set focus to the content element
			contentElement.focus();
			// Scroll to the content element
			contentElement.scrollIntoView({ behavior: 'smooth' });
			// Announce for screen readers
			const announcement = document.createElement('div');
			announcement.setAttribute('aria-live', 'assertive');
			announcement.setAttribute('role', 'status');
			announcement.style.position = 'absolute';
			announcement.style.width = '1px';
			announcement.style.height = '1px';
			announcement.style.padding = '0';
			announcement.style.margin = '-1px';
			announcement.style.overflow = 'hidden';
			announcement.style.clip = 'rect(0, 0, 0, 0)';
			announcement.style.whiteSpace = 'nowrap';
			announcement.style.border = '0';
			announcement.textContent = 'Skipped to main content';
			document.body.appendChild(announcement);
			setTimeout(() => {
				document.body.removeChild(announcement);
			}, 1000);
		}
		setIsVisible(false);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter' || e.key === ' ') {
			handleClick(e);
		}
	};

	return (
		<a
			href={`#${contentId}`}
			className={cn(
				'sr-only focus:not-sr-only focus:fixed focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:rounded-md focus:outline-none focus:shadow-lg',
				className
			)}
			onClick={handleClick}
			onKeyDown={handleKeyDown}
			onBlur={() => setTimeout(() => setIsVisible(false), 100)}
			tabIndex={0}
			aria-label={label}
			role="link"
			style={{
				// Always visually present when focused, regardless of isVisible state
				position: isVisible ? 'fixed' : 'absolute',
				opacity: isVisible ? 1 : 0,
				clip: isVisible ? 'auto' : 'rect(0, 0, 0, 0)',
				transition: 'opacity 0.2s ease-in-out',
			}}
		>
			{label}
		</a>
	);
}
