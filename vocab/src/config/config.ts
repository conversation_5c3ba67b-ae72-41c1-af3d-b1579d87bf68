'use server';

const env = process.env;

// Server configuration
export async function getServerConfig() {
	return {
		port: Number.parseInt(env.PORT || '5000', 10),
		env: env.NODE_ENV || 'development',
	};
}

// Authentication configuration
export async function getAuthConfig() {
	return {
		jwtCookieName: env.JWT_COOKIE_NAME || 'auth_token',
		jwtSecret: env.JWT_SECRET || 'your-secret-key',
		jwtExpiresIn: Number(env.JWT_EXPIRES_IN) || 30 * 24 * 60 * 60,
		// Google OAuth configuration
		google: {
			clientId: env.GOOGLE_CLIENT_ID || '',
			clientSecret: env.GOOGLE_CLIENT_SECRET || '',
		},
	};
}

// Feature flags configuration
export async function getFeatureFlags() {
	return {
		googleLogin: env.FEATURE_GOOGLE_LOGIN === 'true',
	};
}

// LLM configuration
export async function getLLMConfig() {
	return {
		openAIKey: env.LLM_OPENAI_API_KEY || '',
		openAIModel: env.LLM_OPENAI_MODEL || 'gpt-4o-mini',
		maxExamples: Number.parseInt(env.LLM_MAX_EXAMPLES || '8'),
		temperature: Number.parseFloat(env.LLM_TEMPERATURE || '0.7'),
		maxTokens: Number.parseInt(env.LLM_MAX_TOKENS || '1000'),
	};
}
