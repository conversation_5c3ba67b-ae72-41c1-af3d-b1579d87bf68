import { NextRequest, NextResponse } from 'next/server';
import { createApiRoute } from '@/lib/api-error-middleware';
import { errorLogger, createErrorContext } from '@/lib/error-handling';

// ============================================================================
// HEALTH CHECK ENDPOINT
// ============================================================================

interface HealthCheckResponse {
	status: 'healthy' | 'unhealthy';
	timestamp: string;
	uptime: number;
	version?: string;
	environment: string;
	services: {
		database: 'healthy' | 'unhealthy' | 'unknown';
		redis?: 'healthy' | 'unhealthy' | 'unknown';
		external_apis?: 'healthy' | 'unhealthy' | 'unknown';
	};
	memory?: {
		used: number;
		total: number;
		percentage: number;
	};
}

// ============================================================================
// HEALTH CHECK FUNCTIONS
// ============================================================================

async function checkDatabaseHealth(): Promise<'healthy' | 'unhealthy' | 'unknown'> {
	try {
		// Try to import and use Prisma client
		const { PrismaClient } = await import('@prisma/client');
		const prisma = new PrismaClient();
		
		// Simple query to check database connectivity
		await prisma.$queryRaw`SELECT 1`;
		await prisma.$disconnect();
		
		return 'healthy';
	} catch (error) {
		errorLogger.error(
			'Database health check failed',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('HealthCheck', 'database_check'),
			'HealthCheck'
		);
		return 'unhealthy';
	}
}

function getMemoryUsage() {
	if (typeof process !== 'undefined' && process.memoryUsage) {
		const usage = process.memoryUsage();
		const total = usage.heapTotal;
		const used = usage.heapUsed;
		
		return {
			used: Math.round(used / 1024 / 1024), // MB
			total: Math.round(total / 1024 / 1024), // MB
			percentage: Math.round((used / total) * 100),
		};
	}
	return undefined;
}

function getUptime(): number {
	if (typeof process !== 'undefined' && process.uptime) {
		return Math.round(process.uptime());
	}
	return 0;
}

// ============================================================================
// API HANDLERS
// ============================================================================

async function healthCheckHandler(request: NextRequest): Promise<NextResponse> {
	const startTime = Date.now();
	
	try {
		// Check database health
		const databaseStatus = await checkDatabaseHealth();
		
		// Get system information
		const memory = getMemoryUsage();
		const uptime = getUptime();
		
		// Determine overall health status
		const isHealthy = databaseStatus === 'healthy';
		
		const healthData: HealthCheckResponse = {
			status: isHealthy ? 'healthy' : 'unhealthy',
			timestamp: new Date().toISOString(),
			uptime,
			version: process.env.npm_package_version || '1.0.0',
			environment: process.env.NODE_ENV || 'development',
			services: {
				database: databaseStatus,
			},
			...(memory && { memory }),
		};
		
		const responseTime = Date.now() - startTime;
		
		// Log health check
		if (process.env.NODE_ENV === 'development') {
			console.log(`Health check completed in ${responseTime}ms - Status: ${healthData.status}`);
		}
		
		// Return appropriate status code
		const statusCode = isHealthy ? 200 : 503;
		
		return NextResponse.json(healthData, { 
			status: statusCode,
			headers: {
				'Cache-Control': 'no-cache, no-store, must-revalidate',
				'X-Response-Time': `${responseTime}ms`,
			}
		});
		
	} catch (error) {
		const responseTime = Date.now() - startTime;
		
		errorLogger.error(
			'Health check endpoint failed',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('HealthCheck', 'health_check_handler'),
			'HealthCheck'
		);
		
		const errorResponse: HealthCheckResponse = {
			status: 'unhealthy',
			timestamp: new Date().toISOString(),
			uptime: getUptime(),
			environment: process.env.NODE_ENV || 'development',
			services: {
				database: 'unknown',
			},
		};
		
		return NextResponse.json(errorResponse, { 
			status: 503,
			headers: {
				'Cache-Control': 'no-cache, no-store, must-revalidate',
				'X-Response-Time': `${responseTime}ms`,
			}
		});
	}
}

// ============================================================================
// ROUTE EXPORTS
// ============================================================================

// GET /api/health - Full health check with detailed information
export const GET = createApiRoute(healthCheckHandler);

// HEAD /api/health - Lightweight health check (just status code)
export async function HEAD(request: NextRequest): Promise<NextResponse> {
	try {
		// Quick database check for HEAD requests
		const databaseStatus = await checkDatabaseHealth();
		const isHealthy = databaseStatus === 'healthy';
		
		const statusCode = isHealthy ? 200 : 503;
		
		return new NextResponse(null, { 
			status: statusCode,
			headers: {
				'Cache-Control': 'no-cache, no-store, must-revalidate',
				'X-Health-Status': isHealthy ? 'healthy' : 'unhealthy',
			}
		});
		
	} catch (error) {
		errorLogger.error(
			'Health check HEAD request failed',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('HealthCheck', 'head_handler'),
			'HealthCheck'
		);
		
		return new NextResponse(null, { 
			status: 503,
			headers: {
				'Cache-Control': 'no-cache, no-store, must-revalidate',
				'X-Health-Status': 'unhealthy',
			}
		});
	}
}

// OPTIONS /api/health - CORS preflight
export async function OPTIONS(): Promise<NextResponse> {
	return new NextResponse(null, {
		status: 200,
		headers: {
			'Allow': 'GET, HEAD, OPTIONS',
			'Cache-Control': 'no-cache, no-store, must-revalidate',
		},
	});
}

// ============================================================================
// HEALTH CHECK UTILITIES
// ============================================================================

/**
 * Simple health check function that can be used internally
 */
export async function isApplicationHealthy(): Promise<boolean> {
	try {
		const databaseStatus = await checkDatabaseHealth();
		return databaseStatus === 'healthy';
	} catch {
		return false;
	}
}

/**
 * Get basic health information
 */
export async function getHealthInfo(): Promise<Omit<HealthCheckResponse, 'timestamp'>> {
	const databaseStatus = await checkDatabaseHealth();
	const memory = getMemoryUsage();
	const uptime = getUptime();
	const isHealthy = databaseStatus === 'healthy';
	
	return {
		status: isHealthy ? 'healthy' : 'unhealthy',
		uptime,
		version: process.env.npm_package_version || '1.0.0',
		environment: process.env.NODE_ENV || 'development',
		services: {
			database: databaseStatus,
		},
		...(memory && { memory }),
	};
}
