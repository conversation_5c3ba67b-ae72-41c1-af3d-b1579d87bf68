import { GrammarPracticeResultItem } from '@/backend/services';
import { Button, Translate } from '@/components/ui';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { cn, highlightTextDifferences } from '@/lib';
import { Language } from '@prisma/client';
import { Eye, EyeOff, Target } from 'lucide-react';
import { useState } from 'react';
import type { SelectedWord } from './types';

export const GrammarPracticeItem = ({
	item,
	index,
	selectedWords,
	onWordSelection,
	targetLanguage,
	sourceLanguage,
}: {
	item: GrammarPracticeResultItem;
	index: number;
	selectedWords: SelectedWord[];
	onWordSelection: (paragraphIndex: number, word: string, wordIndex: number) => void;
	targetLanguage: Language;
	sourceLanguage: Language;
}) => {
	const [showResults, setShowResults] = useState(false);
	const words = item.paragraphWithErrors.split(/\s+/);
	const diffs = highlightTextDifferences(item.paragraphWithErrors, item.correctedParagraph);

	// Find error for a specific diff text
	const getErrorForDiffText = (diffText: string) => {
		return item.allErrors.find((error) => {
			const errorText = error.errorText.toLowerCase().trim();
			const diffTextLower = diffText.toLowerCase().trim();
			return errorText.includes(diffTextLower) || diffTextLower.includes(errorText);
		});
	};

	return (
		<div className="bg-white dark:bg-background/50 border-2 border-border dark:border-border rounded-xl p-6 space-y-6 shadow-sm hover:shadow-md transition-all duration-200">
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-3">
					<div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50 text-blue-700 dark:text-blue-300 rounded-full border-2 border-blue-200 dark:border-blue-700 font-bold text-sm">
						{index + 1}
					</div>
					<div className="flex items-center gap-2">
						<span className="inline-flex items-center gap-1 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground text-xs px-2 py-1 rounded-full font-medium">
							<Target className="w-3 h-3" />
							<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />
						</span>
						<span className="text-xs text-muted-foreground dark:text-muted-foreground">
							{selectedWords.length} selected
						</span>
					</div>
				</div>
				<Button
					variant={showResults ? 'default' : 'outline'}
					size="sm"
					onClick={() => setShowResults(!showResults)}
					className="flex items-center gap-2 transition-all duration-200"
				>
					{showResults ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
					<Translate
						text={showResults ? 'grammar.hide_results' : 'grammar.show_results'}
					/>
				</Button>
			</div>
			<div className="space-y-4">
				<div className="bg-background/50 dark:bg-background/30 border border-border dark:border-border rounded-lg p-4">
					<div className="text-primary dark:text-primary leading-relaxed text-base">
						{words.map((word, wordIndex) => {
							const isSelected = selectedWords.some((s) => s.index === wordIndex);
							return (
								<span key={`${word}-${wordIndex}`}>
									<span
										className={cn(
											'relative inline-block cursor-pointer transition-all duration-200 ease-in-out transform rounded-md px-1.5 py-0.5 mt-1',
											{
												'bg-gradient-to-r from-amber-100 to-yellow-100 dark:from-amber-900/50 dark:to-yellow-900/50 outline outline-amber-300 dark:outline-amber-600 shadow-sm':
													isSelected,
												'hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-950/30 dark:hover:to-indigo-950/30 hover:outline hover:outline-blue-200 dark:hover:outline-blue-700':
													!isSelected,
											}
										)}
										onClick={() => onWordSelection(index, word, wordIndex)}
									>
										{word}
									</span>
									{wordIndex < words.length - 1 && ' '}
								</span>
							);
						})}
					</div>
				</div>
				{showResults && (
					<div className="bg-background/50 dark:bg-background/30 border border-border dark:border-border rounded-lg p-4">
						<div className="text-primary dark:text-primary leading-relaxed text-base">
							{diffs.map((diff, index) => {
								if (diff.type === 'equal') {
									return (
										<span
											className={cn(
												'relative inline-block transition-all duration-200 ease-in-out transform rounded-md px-1.5 py-0.5 mt-1'
											)}
											key={index}
										>
											{diff.text}{' '}
										</span>
									);
								} else if (diff.type === 'change') {
									// Handle combined wrong/correct changes
									const error = getErrorForDiffText(diff.wrong || diff.text);
									const diffWords = diff.text
										.split(/\s+/)
										.filter((w) => w.trim());

									return (
										<span key={index}>
											{diffWords.map((word, wordIndex) => (
												<span key={`${word}-${wordIndex}`}>
													<span
														className={cn(
															'relative inline-block cursor-pointer transition-all duration-200 ease-in-out transform rounded-md px-1.5 py-0.5 mt-1 group',
															'bg-gradient-to-r from-red-100 to-red-200 dark:from-red-900/50 dark:to-red-800/50 outline outline-red-300 dark:outline-red-600',
															'hover:shadow-md'
														)}
													>
														<del className="text-red-600 dark:text-red-400">
															{diff.wrong}
														</del>
														<span className="mx-1 text-gray-500">
															→
														</span>
														<span className="text-green-600 dark:text-green-400">
															{diff.correct}
														</span>
														{error && (
															<span
																className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-white dark:bg-background text-primary dark:text-primary text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-[320px] break-words border border-border dark:border-border"
																style={{
																	left: 'clamp(0px, 50%, calc(100vw - 320px))',
																}}
															>
																<span className="inline-flex items-center gap-1 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground text-xs px-2 py-1 rounded-full font-medium">
																	{error.errorType}
																</span>
																<div className="my-2">
																	<div className="font-medium mb-1 text-primary dark:text-primary">
																		<Translate
																			text={getTranslationKeyOfLanguage(
																				targetLanguage
																			)}
																		/>
																		:
																	</div>
																	<div className="text-xs text-primary/80 dark:text-primary/80">
																		{
																			error.explanation
																				.target_language
																		}
																	</div>
																</div>
																<div>
																	<div className="font-medium mb-1 text-primary dark:text-primary">
																		<Translate
																			text={getTranslationKeyOfLanguage(
																				sourceLanguage
																			)}
																		/>
																		:
																	</div>
																	<div className="text-xs text-primary/80 dark:text-primary/80">
																		{
																			error.explanation
																				.source_language
																		}
																	</div>
																</div>
															</span>
														)}
													</span>
													{wordIndex < diffWords.length - 1 && ' '}
												</span>
											))}{' '}
										</span>
									);
								}
							})}
						</div>
					</div>
				)}
			</div>
		</div>
	);
};
