import { ERROR_TYPE_COLORS } from './constants';

/**
 * Get the appropriate color classes for an error type
 */
export const getErrorColor = (errorType: string) => {
	switch (errorType.toLowerCase()) {
		case 'grammar':
			return ERROR_TYPE_COLORS.grammar;
		case 'vocabulary':
			return ERROR_TYPE_COLORS.vocabulary;
		case 'spelling':
			return ERROR_TYPE_COLORS.spelling;
		case 'mechanics':
			return ERROR_TYPE_COLORS.mechanics;
		default:
			return ERROR_TYPE_COLORS.default;
	}
};
