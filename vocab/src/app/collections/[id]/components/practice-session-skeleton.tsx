'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface PracticeSessionSkeletonProps {
	type?: 'mcq' | 'review' | 'paragraph' | 'grammar';
}

export function PracticeSessionSkeleton({ type = 'mcq' }: PracticeSessionSkeletonProps) {
	if (type === 'mcq') {
		return (
			<div className="space-y-6">
				<Card>
					<CardHeader>
						<div className="h-6 w-48 bg-muted rounded animate-pulse mx-auto" />
					</CardHeader>
					<CardContent>
						<Card className="max-w-2xl mx-auto">
							<CardHeader>
								<div className="text-center space-y-3">
									<div className="h-4 w-32 bg-muted rounded animate-pulse mx-auto" />
									<div className="h-8 w-40 bg-muted rounded animate-pulse mx-auto" />
									<div className="h-4 w-24 bg-muted rounded animate-pulse mx-auto" />
								</div>
							</CardHeader>
							<CardContent>
								<div className="space-y-3">
									{[...Array(4)].map((_, i) => (
										<div
											key={i}
											className="flex items-center p-4 border rounded-lg"
										>
											<div className="h-4 w-4 bg-muted rounded-full animate-pulse mr-3" />
											<div className="h-4 w-full bg-muted rounded animate-pulse" />
										</div>
									))}
								</div>
								<div className="mt-6 flex justify-end">
									<div className="h-10 w-32 bg-muted rounded animate-pulse" />
								</div>
							</CardContent>
						</Card>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (type === 'review') {
		return (
			<div className="py-4">
				<div className="flex items-center justify-end mb-4 min-h-[24px]">
					<div className="h-4 w-16 bg-muted rounded animate-pulse" />
				</div>
				<Card className="shadow-xl">
					<CardHeader className="pb-3">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-3">
								<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								<div className="h-6 w-32 bg-muted rounded animate-pulse" />
								<div className="h-4 w-24 bg-muted rounded animate-pulse" />
							</div>
							<div className="flex gap-2">
								<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								<div className="h-8 w-8 bg-muted rounded animate-pulse" />
							</div>
						</div>
					</CardHeader>
					<CardContent>
						<div className="space-y-4">
							<div className="h-4 w-full bg-muted rounded animate-pulse" />
							<div className="h-4 w-3/4 bg-muted rounded animate-pulse" />
							<div className="space-y-2 ml-4">
								<div className="h-3 w-full bg-muted rounded animate-pulse" />
								<div className="h-3 w-5/6 bg-muted rounded animate-pulse" />
							</div>
						</div>
					</CardContent>
				</Card>
				<div className="mt-6 flex justify-end gap-4">
					<div className="h-12 w-44 bg-muted rounded animate-pulse" />
					<div className="h-12 w-32 bg-muted rounded animate-pulse" />
				</div>
			</div>
		);
	}

	if (type === 'paragraph') {
		return (
			<div className="space-y-6">
				<Card>
					<CardHeader>
						<div className="h-6 w-48 bg-muted rounded animate-pulse" />
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="h-32 w-full bg-muted rounded animate-pulse" />
						<div className="h-32 w-full bg-muted rounded animate-pulse" />
						<div className="flex justify-end">
							<div className="h-10 w-32 bg-muted rounded animate-pulse" />
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Default/grammar skeleton
	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<div className="h-6 w-48 bg-muted rounded animate-pulse" />
					<div className="space-y-2">
						<div className="h-2 w-full bg-muted rounded animate-pulse" />
						<div className="flex justify-between text-sm">
							<div className="h-4 w-24 bg-muted rounded animate-pulse" />
							<div className="h-4 w-32 bg-muted rounded animate-pulse" />
						</div>
					</div>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="h-6 w-full bg-muted rounded animate-pulse" />
						<div className="space-y-2">
							{[...Array(4)].map((_, i) => (
								<div key={i} className="h-10 w-full bg-muted rounded animate-pulse" />
							))}
						</div>
						<div className="flex justify-end">
							<div className="h-10 w-32 bg-muted rounded animate-pulse" />
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
