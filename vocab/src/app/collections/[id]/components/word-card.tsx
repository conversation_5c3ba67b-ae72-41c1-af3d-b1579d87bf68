'use client';

import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON>Content,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	LoadingSpinner,
	Translate,
} from '@/components/ui';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { ChevronDown, ChevronUp, Eye, EyeOff, PlusCircle, Trash2 } from 'lucide-react';
import { memo, useCallback, useState } from 'react';

interface WordCardProps {
	word: WordDetail;
	onAddToCollection?: () => void;
	isAddingToCollection?: boolean;
	onDeleteWord?: () => void;
	isDeleting?: boolean;
	className?: string;
	isReviewMode?: boolean; // Added for review mode
	showSourceLanguage?: boolean; // Added to control target language text visibility in review mode
	onToggleTargetLanguage?: () => void; // Added to toggle target language text visibility
	defaultExpanded?: boolean; // Added to control initial expanded state
	sourceLanguage: Language; // Source language from collection
	targetLanguage: Language; // Target language from collection
}

function WordCardComponent({
	word,
	onAddToCollection,
	isAddingToCollection,
	onDeleteWord,
	isDeleting,
	className,
	isReviewMode = false, // Default to false
	showSourceLanguage = true, // Default to true (relevant only in review mode)
	onToggleTargetLanguage,
	defaultExpanded, // Accepted defaultExpanded prop
	sourceLanguage, // Default fallback
	targetLanguage, // Default fallback
}: WordCardProps) {
	// Use defaultExpanded prop for initial state, fallback to false
	// In review mode, always expanded and no toggle functionality
	const [isExpanded, setIsExpanded] = useState(isReviewMode ? true : defaultExpanded ?? false);

	const handleToggleExpand = useCallback(() => {
		if (!isReviewMode) {
			setIsExpanded((prev) => !prev);
		}
	}, [isReviewMode]);

	const handleAddToCollection = useCallback(() => {
		if (onAddToCollection) {
			onAddToCollection();
		}
	}, [onAddToCollection]);

	const handleDeleteWord = useCallback(() => {
		if (onDeleteWord) {
			onDeleteWord();
		}
	}, [onDeleteWord]);
	return (
		<Card
			className={cn(
				'h-full flex flex-col break-inside-avoid shadow-lg border border-border bg-background hover:shadow-xl transition-shadow duration-200',
				className
			)}
		>
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle className="flex justify-between items-start gap-3">
					<span className="text-3xl font-bold tracking-tight text-primary drop-shadow-sm flex-grow">
						{word.term}
					</span>
					{!isReviewMode && (
						<div className="flex flex-col items-end space-y-1.5 flex-shrink-0">
							<Button
								variant="ghost"
								size="icon"
								onClick={handleToggleExpand}
								aria-label={isExpanded ? 'Collapse' : 'Expand'}
							>
								{isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
							</Button>
						</div>
					)}
				</CardTitle>
			</CardHeader>
			{(isExpanded || isReviewMode) && (
				<CardContent className="flex-grow flex flex-col p-5">
					<div className="space-y-4 flex-grow">
						{word.definitions && word.definitions.length > 0 ? (
							word.definitions.map((definition, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									{definition.pos && definition.pos.length > 0 && (
										<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
											{definition.pos.join(', ')}
										</p>
									)}
									{definition.ipa && (
										<p className="text-sm text-muted-foreground italic mb-2.5">
											IPA: {definition.ipa}
										</p>
									)}
									{/* Explanations section */}
									{definition.explains && definition.explains.length > 0 ? (
										<div className="mb-3">
											{definition.explains.map((explain, expIndex) => (
												<div
													key={expIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																targetLanguage
															)}
														/>
														:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{explain[targetLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.explanation_not_provided" />
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																sourceLanguage
															)}
														/>
														:
													</p>
													{(!isReviewMode || showSourceLanguage) && (
														<p className="text-sm text-foreground/95">
															{explain[sourceLanguage] || (
																<span className="italic opacity-70">
																	<Translate text="words.translation_not_provided" />
																</span>
															)}
														</p>
													)}
													{isReviewMode && !showSourceLanguage && (
														<p className="text-sm text-muted-foreground italic">
															<Translate text="collections.hidden" />
														</p>
													)}
												</div>
											))}
										</div>
									) : (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												<Translate text="words.explanations" />:
											</p>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												<Translate text="words.no_explanations_provided" />
											</p>
										</div>
									)}

									{/* Examples section */}
									{definition.examples && definition.examples.length > 0 ? (
										<div>
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												<Translate text="words.examples" />:
											</p>
											{definition.examples.map((example, exIndex) => (
												<div
													key={exIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																targetLanguage
															)}
														/>
														:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{example[targetLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.example_not_provided" />
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																sourceLanguage
															)}
														/>
														:
													</p>
													{(!isReviewMode || showSourceLanguage) && (
														<p className="text-sm text-foreground/95">
															{example[sourceLanguage] || (
																<span className="italic opacity-70">
																	<Translate text="words.translation_not_provided" />
																</span>
															)}
														</p>
													)}
													{isReviewMode && !showSourceLanguage && (
														<p className="text-sm text-muted-foreground italic">
															<Translate text="collections.hidden" />
														</p>
													)}
												</div>
											))}
										</div>
									) : (
										<div>
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												<Translate text="collections.examples" />
											</p>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												<Translate text="words.no_examples_provided" />
											</p>
										</div>
									)}
								</div>
							))
						) : (
							<p className="p-4 text-sm text-muted-foreground italic">
								<Translate text="words.no_definitions_available" />
							</p>
						)}
					</div>

					{/* Action buttons */}
					<div className="mt-auto flex flex-col space-y-2 pt-5">
						{isReviewMode && onToggleTargetLanguage && (
							<Button
								onClick={onToggleTargetLanguage}
								variant="outline"
								size="sm"
								className="w-full flex items-center justify-center gap-2"
							>
								{showSourceLanguage ? (
									<>
										<EyeOff size={16} />
										<span>
											<Translate text="words.hide" />{' '}
											<Translate
												text={getTranslationKeyOfLanguage(sourceLanguage)}
											/>
										</span>
									</>
								) : (
									<>
										<Eye size={16} />
										<span>
											<Translate text="words.show" />{' '}
											<Translate
												text={getTranslationKeyOfLanguage(sourceLanguage)}
											/>
										</span>
									</>
								)}
							</Button>
						)}
						{!isReviewMode && onAddToCollection && (
							<Button
								onClick={handleAddToCollection}
								disabled={isAddingToCollection}
								size="sm"
								className="w-full flex items-center justify-center gap-2"
							>
								{isAddingToCollection ? (
									<LoadingSpinner size="sm" />
								) : (
									<PlusCircle size={16} />
								)}
								<span>
									{isAddingToCollection ? (
										<Translate text="collections.adding_to_collection" />
									) : (
										<Translate text="collections.add_to_collection" />
									)}
								</span>
							</Button>
						)}
						{!isReviewMode && onDeleteWord && (
							<Button
								onClick={handleDeleteWord}
								disabled={isDeleting}
								variant="outline"
								size="sm"
								className="w-full flex items-center justify-center gap-2 transition-colors duration-150 hover:bg-destructive/5 dark:hover:bg-destructive/10 text-destructive hover:text-destructive/90"
							>
								{isDeleting ? <LoadingSpinner size="sm" /> : <Trash2 size={16} />}
								<span>
									{isDeleting ? (
										<Translate text="collections.deleting_word" />
									) : (
										<Translate text="collections.delete_word" />
									)}
								</span>
							</Button>
						)}
					</div>
				</CardContent>
			)}{' '}
			{/* Close isExpanded conditional */}
		</Card>
	);
}

const arePropsEqual = (prevProps: WordCardProps, nextProps: WordCardProps) => {
	return (
		prevProps.word.id === nextProps.word.id &&
		prevProps.isAddingToCollection === nextProps.isAddingToCollection &&
		prevProps.isDeleting === nextProps.isDeleting &&
		prevProps.className === nextProps.className &&
		prevProps.isReviewMode === nextProps.isReviewMode && // Added for review mode
		prevProps.showSourceLanguage === nextProps.showSourceLanguage && // Added for review mode
		prevProps.defaultExpanded === nextProps.defaultExpanded && // Added for comparison
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		// Shallow compare functions assuming they are stable (e.g., from useCallback with stable deps)
		prevProps.onAddToCollection === nextProps.onAddToCollection &&
		prevProps.onDeleteWord === nextProps.onDeleteWord &&
		prevProps.onToggleTargetLanguage === nextProps.onToggleTargetLanguage // Added for review mode
	);
};

export const WordCard = memo(WordCardComponent, arePropsEqual);
