'use client';

import { createLastSeenWordApi } from '@/backend/api';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Translate } from '@/components/ui/translate';
import { useToast } from '@/components/ui/use-toast';
import { WordCard } from '@/app/collections/[id]/components/word-card';
import { useTranslation } from '@/contexts';
import { useLoading } from '@/contexts/loading-context';
import { AnimatePresence, motion } from 'framer-motion';
import { CheckCircle } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';
import { useCollections } from '@/hooks';

const REVIEW_LIMIT = 10;

export function ReviewClient({ params }: { params: { id: string } }) {
	const collectionId = params.id;
	const { currentCollection, loading, error } = useCollections();
	const isLoading = loading.get || loading.setCurrent;
	const { t } = useTranslation();
	const { toast } = useToast();
	const isActive = true; // Always active in this context

	const { setLoading: setGlobalLoading } = useLoading();
	const {
		currentCollectionWords: wordsToReview,
		loading: { getWordsToReviewWords: getWordsToReviewLoading },
		error: fetchError,
		getCurrentCollectionWordsToReview: getWordsToReview,
	} = useCollections();

	const [currentWordIndex, setCurrentWordIndex] = useState(0);
	const [unlockedWords, setUnlockedWords] = useState<Set<string>>(new Set());
	const [isMarkingSeen, setIsMarkingSeen] = useState(false);
	const [reviewComplete, setReviewComplete] = useState(false);
	const [hasFetchedOrActivated, setHasFetchedOrActivated] = useState(false);

	useEffect(() => {
		if (isActive) {
			setHasFetchedOrActivated(true);
			const needsToFetch = collectionId && wordsToReview.length === 0;

			if (needsToFetch) {
				setReviewComplete(false);
				setCurrentWordIndex(0);
				setUnlockedWords(new Set());
				getWordsToReview(REVIEW_LIMIT);
			} else if (wordsToReview.length > 0) {
				if (currentWordIndex >= wordsToReview.length) {
					setReviewComplete(true);
				} else {
					setReviewComplete(false);
				}
			}
		}
	}, [collectionId, isActive, getWordsToReview, wordsToReview, currentWordIndex]);

	useEffect(() => {
		if (isActive) {
			setGlobalLoading(getWordsToReviewLoading || isMarkingSeen);
		} else {
			if (getWordsToReviewLoading || isMarkingSeen) {
				setGlobalLoading(false);
			}
		}
	}, [getWordsToReviewLoading, isMarkingSeen, setGlobalLoading, isActive]);

	const handleToggleVietnamese = useCallback((wordId: string) => {
		setUnlockedWords((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(wordId)) {
				newSet.delete(wordId);
			} else {
				newSet.add(wordId);
			}
			return newSet;
		});
	}, []);

	const handleNextWord = useCallback(async () => {
		if (currentWordIndex >= wordsToReview.length) return;

		const currentWord = wordsToReview[currentWordIndex];
		setIsMarkingSeen(true);
		try {
			await createLastSeenWordApi(currentWord.id, currentCollection?.id);
			toast({
				title: t('review.word_marked_seen_title'),
				description: t('review.word_marked_seen_desc', { term: currentWord.term }),
			});
		} catch (error) {
			console.error('Failed to mark word as seen:', error);
			toast({
				variant: 'destructive',
				title: t('review.mark_seen_error_title'),
				description:
					error instanceof Error ? error.message : t('review.mark_seen_error_desc'),
			});
		} finally {
			setIsMarkingSeen(false);
		}

		if (currentWordIndex < wordsToReview.length - 1) {
			setCurrentWordIndex((prev) => prev + 1);
			setUnlockedWords(new Set());
		} else {
			setReviewComplete(true);
			toast({
				title: t('review.session_complete_title'),
				description: t('review.session_complete_desc'),
			});
		}
	}, [currentWordIndex, wordsToReview, toast, t]);

	const handleRestartReview = useCallback(() => {
		if (collectionId) {
			setReviewComplete(false);
			setCurrentWordIndex(0);
			setUnlockedWords(new Set());
			getWordsToReview(REVIEW_LIMIT);
		}
	}, [collectionId, getWordsToReview]);

	const currentWordToDisplay = useMemo(() => {
		if (
			isActive &&
			!reviewComplete &&
			wordsToReview &&
			wordsToReview.length > 0 &&
			currentWordIndex < wordsToReview.length
		) {
			return wordsToReview[currentWordIndex];
		}
		return null;
	}, [wordsToReview, currentWordIndex, reviewComplete, isActive]);

	const wordCountText = useMemo(() => {
		return isActive && wordsToReview?.length > 0 && !reviewComplete && currentWordToDisplay
			? `(${currentWordIndex + 1}/${wordsToReview.length})`
			: '';
	}, [wordsToReview, currentWordIndex, reviewComplete, currentWordToDisplay, isActive]);

	if (!hasFetchedOrActivated && !isActive) {
		return (
			<div className="py-4 text-center text-muted-foreground">
				{/* Optionally, a message like "Activate tab to load review" */}
			</div>
		);
	}

	if (getWordsToReviewLoading && wordsToReview.length === 0 && isActive) {
		// Show loading only if active
		return <PracticeSessionSkeleton type="review" />;
	}

	if (fetchError && isActive) {
		return (
			<div className="container mx-auto py-8 text-center">
				<p className="text-destructive mb-4">
					{t('review.fetch_error_title')}:{' '}
					{fetchError?.message || t('review.unknown_error')}
				</p>
				<Button onClick={handleRestartReview} variant="outline">
					<Translate text="review.try_refetch_btn" />
				</Button>
			</div>
		);
	}

	if (!currentCollection) return null;

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
			<div className="max-w-6xl mx-auto space-y-8">
				<header className="text-center space-y-4">
					<h1 className="text-4xl font-bold text-primary dark:text-primary">
						<Translate text="words.review_words" />
					</h1>
					<p className="text-muted-foreground dark:text-muted-foreground text-lg">
						<Translate text="words.review_description" />
					</p>
				</header>

				<div className="py-4">
					<div className="flex items-center justify-end mb-4 min-h-[24px]">
						{wordCountText && (
							<div className="text-muted-foreground text-sm sm:text-base">
								{wordCountText}
							</div>
						)}
					</div>
					<AnimatePresence mode="wait">
						{reviewComplete && isActive ? (
							<motion.div
								key="complete"
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -20 }}
								className="text-center py-10"
							>
								<CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
								<h2 className="text-2xl font-semibold mb-4">
									<Translate text="review.session_complete_title" />
								</h2>
								<p className="text-muted-foreground mb-6">
									<Translate text="review.all_words_reviewed_desc" />
								</p>
								<div className="flex gap-4 justify-center">
									<Button onClick={handleRestartReview}>
										<Translate text="review.review_more_btn" />
									</Button>
								</div>
							</motion.div>
						) : currentWordToDisplay && isActive ? (
							<motion.div
								key={currentWordToDisplay.id}
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
								className="mx-auto"
							>
								<WordCard
									word={currentWordToDisplay}
									isReviewMode={true}
									showSourceLanguage={unlockedWords.has(currentWordToDisplay.id)}
									onToggleTargetLanguage={() =>
										handleToggleVietnamese(currentWordToDisplay.id)
									}
									className="shadow-xl"
									defaultExpanded={true}
									sourceLanguage={currentCollection?.source_language}
									targetLanguage={currentCollection?.target_language}
								/>
								<div className="mt-6 flex justify-end gap-4">
									<Button
										onClick={handleNextWord}
										disabled={isMarkingSeen}
										size="lg"
										className="min-w-[180px] bg-green-600 hover:bg-green-700 text-white"
									>
										{isMarkingSeen ? (
											<LoadingSpinner size="sm" />
										) : (
											<>
												<CheckCircle className="mr-2 h-5 w-5" />
												<Translate text="review.mark_seen_next_btn" />
											</>
										)}
									</Button>
									<Button
										variant="outline"
										onClick={() => {
											if (currentWordIndex < wordsToReview.length - 1) {
												setCurrentWordIndex((prev) => prev + 1);
												setUnlockedWords(new Set());
											} else {
												setReviewComplete(true);
												toast({
													title: t('review.session_complete_title'),
													description: t('review.session_complete_desc'),
												});
											}
										}}
										disabled={reviewComplete}
										size="lg"
										className="min-w-[120px]"
									>
										<Translate text="review.skip_btn" />
									</Button>
								</div>
							</motion.div>
						) : isActive && !getWordsToReviewLoading && wordsToReview.length === 0 ? (
							<motion.div
								key="no-words"
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								className="text-center py-10"
							>
								<h2 className="text-xl font-semibold text-muted-foreground">
									<Translate text="review.no_words_to_review_title" />
								</h2>
								<p className="text-muted-foreground mb-4">
									<Translate text="review.no_words_to_review_desc" />
								</p>
								<Button onClick={handleRestartReview}>
									<Translate text="review.try_refetch_btn" />
								</Button>
							</motion.div>
						) : null}
					</AnimatePresence>
				</div>
			</div>
		</div>
	);
}
