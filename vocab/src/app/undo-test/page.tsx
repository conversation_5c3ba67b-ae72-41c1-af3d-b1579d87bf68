'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui';
import { UndoToastManager, UndoDropdown, UndoPanel } from '@/components/ui';
import { useUndoActions, createWordAddedAction } from '@/hooks/use-undo-actions';
import { Plus, Trash2 } from 'lucide-react';

// ============================================================================
// UNDO TEST PAGE
// ============================================================================

export default function UndoTestPage() {
	const [items, setItems] = useState<Array<{ id: string; name: string }>>([]);
	const [counter, setCounter] = useState(1);

	const {
		actions: undoActions,
		addUndoAction,
		executeUndo,
		removeAction,
		isUndoing,
	} = useUndoActions(10);

	// Add item with undo functionality
	const addItem = () => {
		const newItem = {
			id: `item-${counter}`,
			name: `Item ${counter}`,
		};

		// Add item to state
		setItems((prev) => [...prev, newItem]);
		setCounter((prev) => prev + 1);

		// Create undo action
		const undoAction = createWordAddedAction(
			{ term: newItem.name, id: newItem.id },
			'test-collection',
			async (itemId: string) => {
				// Remove item from state
				setItems((prev) => prev.filter((item) => item.id !== itemId));
			}
		);

		addUndoAction(undoAction);
	};

	// Remove item directly (no undo)
	const removeItem = (id: string) => {
		setItems((prev) => prev.filter((item) => item.id !== id));
	};

	// Clear all items
	const clearAll = () => {
		setItems([]);
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background p-8">
			<div className="max-w-4xl mx-auto space-y-8">
				{/* Header */}
				<header className="text-center space-y-4">
					<div className="flex items-center justify-between">
						<div className="flex-1" />
						<div className="flex-1">
							<h1 className="text-4xl font-bold text-primary">Undo System Test</h1>
						</div>
						<div className="flex-1 flex justify-end">
							<UndoDropdown
								actions={undoActions}
								onUndo={executeUndo}
								onDismiss={removeAction}
								isUndoing={isUndoing}
								maxVisible={5}
							/>
						</div>
					</div>
					<p className="text-muted-foreground text-lg">
						Test the undo functionality by adding and removing items
					</p>
				</header>

				{/* Controls */}
				<Card>
					<CardHeader>
						<CardTitle>Controls</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex gap-4">
							<Button onClick={addItem} className="gap-2">
								<Plus className="h-4 w-4" />
								Add Item (with undo)
							</Button>

							<Button
								variant="destructive"
								onClick={clearAll}
								disabled={items.length === 0}
								className="gap-2"
							>
								<Trash2 className="h-4 w-4" />
								Clear All
							</Button>
						</div>

						<div className="text-sm text-muted-foreground">
							<p>• Click &quot;Add Item&quot; to add an item with undo capability</p>
							<p>• Use the undo dropdown in the header to undo recent actions</p>
							<p>• Undo actions auto-expire after 10 seconds</p>
						</div>
					</CardContent>
				</Card>

				{/* Items List */}
				<Card>
					<CardHeader>
						<CardTitle>Items ({items.length})</CardTitle>
					</CardHeader>
					<CardContent>
						{items.length === 0 ? (
							<div className="text-center py-8 text-muted-foreground">
								No items yet. Click &quot;Add Item&quot; to get started.
							</div>
						) : (
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
								{items.map((item) => (
									<Card key={item.id} className="border-2">
										<CardContent className="p-4">
											<div className="flex items-center justify-between">
												<span className="font-medium">{item.name}</span>
												<Button
													variant="ghost"
													size="sm"
													onClick={() => removeItem(item.id)}
													className="h-8 w-8 p-0"
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</div>
											<div className="text-xs text-muted-foreground mt-1">
												ID: {item.id}
											</div>
										</CardContent>
									</Card>
								))}
							</div>
						)}
					</CardContent>
				</Card>

				{/* Undo Panel */}
				{undoActions.length > 0 && (
					<Card>
						<CardHeader>
							<CardTitle>Undo Panel</CardTitle>
						</CardHeader>
						<CardContent>
							<UndoPanel
								actions={undoActions}
								onUndo={executeUndo}
								onDismiss={removeAction}
								isUndoing={isUndoing}
							/>
						</CardContent>
					</Card>
				)}

				{/* Debug Info */}
				<Card>
					<CardHeader>
						<CardTitle>Debug Info</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-2 text-sm">
							<div>
								<strong>Items Count:</strong> {items.length}
							</div>
							<div>
								<strong>Undo Actions Count:</strong> {undoActions.length}
							</div>
							<div>
								<strong>Is Undoing:</strong> {isUndoing ? 'Yes' : 'No'}
							</div>
							<div>
								<strong>Next Counter:</strong> {counter}
							</div>
						</div>

						{undoActions.length > 0 && (
							<div className="mt-4">
								<strong>Recent Actions:</strong>
								<ul className="list-disc list-inside mt-2 space-y-1">
									{undoActions.slice(0, 3).map((action) => (
										<li key={action.id} className="text-xs">
											{action.description} (
											{new Date(action.timestamp).toLocaleTimeString()})
										</li>
									))}
								</ul>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			{/* Undo Toast Manager */}
			<UndoToastManager
				actions={undoActions}
				onUndo={executeUndo}
				onDismiss={removeAction}
				isUndoing={isUndoing}
				maxVisible={3}
			/>
		</div>
	);
}
