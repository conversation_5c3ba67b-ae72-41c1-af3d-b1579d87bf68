'use client';

import {
	Button,
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
	useTheme,
	Translate,
} from '@/components/ui';
import { useTranslation } from '@/contexts';
import { getTranslationKeyOfLanguage } from '@/contexts/translations';
import { useAuth } from '@/contexts/auth-context';
import { Language } from '@prisma/client';
import { Languages, LogOut, Monitor, Moon, Settings, Sun, User } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function ClientSettings() {
	const { theme, setTheme } = useTheme();
	const { language, setLanguage, t } = useTranslation();
	const { logout, user } = useAuth();
	const router = useRouter();

	const handleLogout = async () => {
		try {
			await logout();
			router.push('/login');
		} catch (error) {
			console.error('Logout failed:', error);
		}
	};

	return (
		<>
			{/* Combined Float Menu */}
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button
						size="icon"
						className="fixed bottom-6 right-6 z-50 h-14 w-14 rounded-full shadow-lg"
					>
						<Settings className="h-6 w-6" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end" className="mb-1 mr-1 w-56">
					{user && (
						<>
							<DropdownMenuLabel className="flex items-center gap-2">
								<User className="h-4 w-4" />
								{t('user.account')}
							</DropdownMenuLabel>
							<DropdownMenuItem
								onClick={handleLogout}
								className="cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
							>
								<LogOut className="h-4 w-4 mr-2" />
								{t('auth.logout')}
							</DropdownMenuItem>
							<DropdownMenuSeparator />
						</>
					)}
					<DropdownMenuLabel>Language</DropdownMenuLabel>
					<DropdownMenuItem
						onClick={() => setLanguage(Language.EN)}
						className={`cursor-pointer ${language === Language.EN ? 'bg-accent' : ''}`}
					>
						<Languages className="h-4 w-4 mr-2" />
						<Translate text={getTranslationKeyOfLanguage(Language.EN)} />
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => setLanguage(Language.VI)}
						className={`cursor-pointer ${language === Language.VI ? 'bg-accent' : ''}`}
					>
						<Languages className="h-4 w-4 mr-2" />
						<Translate text={getTranslationKeyOfLanguage(Language.VI)} />
					</DropdownMenuItem>

					<DropdownMenuLabel>Theme</DropdownMenuLabel>
					<DropdownMenuItem
						onClick={() => setTheme('light')}
						className={`cursor-pointer ${theme === 'light' ? 'bg-accent' : ''}`}
					>
						<Sun className="h-4 w-4 mr-2" />
						{t('theme.light')}
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => setTheme('dark')}
						className={`cursor-pointer ${theme === 'dark' ? 'bg-accent' : ''}`}
					>
						<Moon className="h-4 w-4 mr-2" />
						{t('theme.dark')}
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => setTheme('system')}
						className={`cursor-pointer ${theme === 'system' ? 'bg-accent' : ''}`}
					>
						<Monitor className="h-4 w-4 mr-2" />
						{t('theme.system')}
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</>
	);
}
