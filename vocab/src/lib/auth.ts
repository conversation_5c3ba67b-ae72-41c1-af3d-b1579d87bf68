'use server';

import { verifyToken } from '@/backend/utils/token.util';
import { getAuthConfig } from '@/config';
import { cookies } from 'next/headers';

/**
 * Authentication function for server components and API routes
 * In production, it verifies the JWT token from cookies
 */
export async function auth(): Promise<{
	user: { id: string };
} | null> {
	const authConfig = await getAuthConfig();

	// In production, verify the token
	const cookieStore = await cookies();
	const token = cookieStore.get(authConfig.jwtCookieName)?.value;
	if (!token) return null;

	try {
		const payload = await verifyToken(token);

		const userId = payload.sub;
		if (!userId) return null;

		return { user: { id: userId } };
	} catch {
		return null;
	}
}
