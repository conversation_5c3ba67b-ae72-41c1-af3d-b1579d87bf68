'use client';

import {
	AppError,
	ErrorCategory,
	ErrorSeverity,
	NetworkError,
	ServerError,
	TimeoutError,
	UnauthorizedError,
	ValidationError,
	createErrorContext,
	errorLogger,
} from './error-handling';

// ============================================================================
// TYPES
// ============================================================================

export interface ApiInterceptorConfig {
	baseUrl?: string;
	timeout?: number;
	retries?: number;
	onError?: (error: AppError) => void;
	onUnauthorized?: () => void;
	onNetworkError?: () => void;
}

export interface RequestConfig extends RequestInit {
	timeout?: number;
	retries?: number;
	skipErrorHandling?: boolean;
}

// ============================================================================
// API INTERCEPTOR CLASS
// ============================================================================

export class ApiInterceptor {
	private config: Required<ApiInterceptorConfig>;

	constructor(config: ApiInterceptorConfig = {}) {
		this.config = {
			baseUrl: '',
			timeout: 30000,
			retries: 3,
			onError: () => {},
			onUnauthorized: () => {},
			onNetworkError: () => {},
			...config,
		};
	}

	// Main fetch wrapper with error handling
	async fetch<T = any>(url: string, options: RequestConfig = {}): Promise<T> {
		const {
			timeout = this.config.timeout,
			retries = this.config.retries,
			skipErrorHandling = false,
			...fetchOptions
		} = options;

		const fullUrl = this.buildUrl(url);
		const requestId = this.generateRequestId();

		// Log request start
		errorLogger.debug(
			'API request started',
			{ url: fullUrl, method: fetchOptions.method || 'GET', requestId },
			'ApiInterceptor'
		);

		let lastError: unknown;

		for (let attempt = 1; attempt <= retries + 1; attempt++) {
			try {
				const response = await this.makeRequest(fullUrl, fetchOptions, timeout, requestId);
				
				// Log successful request
				if (attempt > 1) {
					errorLogger.info(
						'API request succeeded after retry',
						{ url: fullUrl, attempt, requestId },
						'ApiInterceptor'
					);
				}

				return await this.handleResponse<T>(response, requestId);
			} catch (error) {
				lastError = error;

				// Log attempt failure
				errorLogger.warn(
					`API request attempt ${attempt} failed`,
					{ url: fullUrl, attempt, error, requestId },
					'ApiInterceptor'
				);

				// Don't retry on certain errors
				if (error instanceof AppError && !this.shouldRetry(error)) {
					break;
				}

				// Don't retry on last attempt
				if (attempt === retries + 1) {
					break;
				}

				// Wait before retry with exponential backoff
				await this.delay(Math.pow(2, attempt - 1) * 1000);
			}
		}

		// All attempts failed
		const appError = this.normalizeError(lastError, fullUrl, requestId);
		
		if (!skipErrorHandling) {
			this.handleError(appError);
		}

		throw appError;
	}

	// Convenience methods
	async get<T = any>(url: string, options: Omit<RequestConfig, 'method' | 'body'> = {}): Promise<T> {
		return this.fetch<T>(url, { ...options, method: 'GET' });
	}

	async post<T = any>(url: string, data?: any, options: Omit<RequestConfig, 'method'> = {}): Promise<T> {
		return this.fetch<T>(url, {
			...options,
			method: 'POST',
			body: data ? JSON.stringify(data) : undefined,
			headers: {
				'Content-Type': 'application/json',
				...options.headers,
			},
		});
	}

	async put<T = any>(url: string, data?: any, options: Omit<RequestConfig, 'method'> = {}): Promise<T> {
		return this.fetch<T>(url, {
			...options,
			method: 'PUT',
			body: data ? JSON.stringify(data) : undefined,
			headers: {
				'Content-Type': 'application/json',
				...options.headers,
			},
		});
	}

	async delete<T = any>(url: string, options: Omit<RequestConfig, 'method' | 'body'> = {}): Promise<T> {
		return this.fetch<T>(url, { ...options, method: 'DELETE' });
	}

	// Private methods
	private buildUrl(url: string): string {
		if (url.startsWith('http')) {
			return url;
		}
		return `${this.config.baseUrl}${url}`;
	}

	private generateRequestId(): string {
		return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	private async makeRequest(
		url: string,
		options: RequestInit,
		timeout: number,
		requestId: string
	): Promise<Response> {
		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), timeout);

		try {
			const response = await fetch(url, {
				...options,
				signal: controller.signal,
			});

			clearTimeout(timeoutId);
			return response;
		} catch (error) {
			clearTimeout(timeoutId);

			if (error instanceof Error && error.name === 'AbortError') {
				throw new TimeoutError(
					`Request timeout after ${timeout}ms`,
					createErrorContext('ApiInterceptor', 'request_timeout', { url, timeout, requestId })
				);
			}

			throw new NetworkError(
				'Network request failed',
				createErrorContext('ApiInterceptor', 'network_error', { url, error, requestId })
			);
		}
	}

	private async handleResponse<T>(response: Response, requestId: string): Promise<T> {
		const url = response.url;

		// Handle different status codes
		if (!response.ok) {
			let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
			let errorData: any = null;

			try {
				const contentType = response.headers.get('content-type');
				if (contentType?.includes('application/json')) {
					errorData = await response.json();
					errorMessage = errorData.message || errorData.error || errorMessage;
				} else {
					errorMessage = await response.text() || errorMessage;
				}
			} catch {
				// Ignore parsing errors, use default message
			}

			const context = createErrorContext('ApiInterceptor', 'http_error', {
				url,
				status: response.status,
				statusText: response.statusText,
				errorData,
				requestId,
			});

			// Create appropriate error based on status code
			if (response.status === 401) {
				throw new UnauthorizedError(errorMessage, context);
			} else if (response.status === 400) {
				throw new ValidationError(errorMessage, context);
			} else if (response.status >= 500) {
				throw new ServerError(errorMessage, context);
			} else {
				throw new AppError(
					errorMessage,
					'HTTP_ERROR',
					response.status,
					ErrorSeverity.MEDIUM,
					ErrorCategory.SERVER,
					context
				);
			}
		}

		// Parse successful response
		try {
			const contentType = response.headers.get('content-type');
			
			if (contentType?.includes('application/json')) {
				return await response.json();
			} else if (contentType?.includes('text/')) {
				return (await response.text()) as unknown as T;
			} else {
				return (await response.blob()) as unknown as T;
			}
		} catch (error) {
			throw new AppError(
				'Failed to parse response',
				'PARSE_ERROR',
				500,
				ErrorSeverity.MEDIUM,
				ErrorCategory.CLIENT,
				createErrorContext('ApiInterceptor', 'parse_error', { url, error, requestId })
			);
		}
	}

	private normalizeError(error: unknown, url: string, requestId: string): AppError {
		if (error instanceof AppError) {
			return error;
		}

		if (error instanceof Error) {
			return new AppError(
				error.message,
				'UNKNOWN_API_ERROR',
				500,
				ErrorSeverity.MEDIUM,
				ErrorCategory.UNKNOWN,
				createErrorContext('ApiInterceptor', 'unknown_error', { url, error, requestId })
			);
		}

		return new AppError(
			'Unknown API error occurred',
			'UNKNOWN_API_ERROR',
			500,
			ErrorSeverity.MEDIUM,
			ErrorCategory.UNKNOWN,
			createErrorContext('ApiInterceptor', 'unknown_error', { url, error, requestId })
		);
	}

	private shouldRetry(error: AppError): boolean {
		// Don't retry client errors (4xx) except for specific cases
		if (error.statusCode >= 400 && error.statusCode < 500) {
			return error.statusCode === 408 || error.statusCode === 429; // Timeout or rate limit
		}

		// Retry server errors (5xx) and network errors
		return (
			error.statusCode >= 500 ||
			error.category === ErrorCategory.NETWORK ||
			error.code === 'TIMEOUT_ERROR'
		);
	}

	private handleError(error: AppError): void {
		// Call appropriate callback based on error type
		if (error.statusCode === 401) {
			this.config.onUnauthorized();
		} else if (error.category === ErrorCategory.NETWORK) {
			this.config.onNetworkError();
		}

		// Call general error callback
		this.config.onError(error);
	}

	private delay(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}

// ============================================================================
// DEFAULT INSTANCE
// ============================================================================

export const apiClient = new ApiInterceptor({
	baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '',
	timeout: 30000,
	retries: 3,
});
