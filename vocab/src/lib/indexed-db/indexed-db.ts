export const DB_NAME = 'vocab-db';
export const DB_VERSION = 1;

/**
 * A generic manager for interacting with IndexedDB.
 */
export class IndexedDBManager {
	private dbName: string;
	private dbVersion: number;
	private storeConfigs: { storeName: string; options?: IDBObjectStoreParameters }[];
	private db: IDBDatabase | null = null;

	constructor(
		dbName: string,
		dbVersion: number,
		storeConfigs: { storeName: string; options?: IDBObjectStoreParameters }[]
	) {
		this.dbName = dbName;
		this.dbVersion = dbVersion;
		this.storeConfigs = storeConfigs;
	}

	/**
	 * Opens the IndexedDB database and creates object stores if necessary.
	 * @returns A Promise that resolves with the database instance.
	 */
	private async openDB(): Promise<IDBDatabase> {
		if (this.db) {
			return Promise.resolve(this.db);
		}

		return new Promise((resolve, reject) => {
			const request = indexedDB.open(this.dbName, this.dbVersion);

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				this.storeConfigs.forEach((config) => {
					if (!db.objectStoreNames.contains(config.storeName)) {
						db.createObjectStore(config.storeName, config.options);
					}
				});
			};

			request.onsuccess = (event) => {
				this.db = (event.target as IDBOpenDBRequest).result;
				resolve(this.db!);
			};

			request.onerror = (event) => {
				reject((event.target as IDBOpenDBRequest).error);
			};
		});
	}

	/**
	 * Gets the IndexedDB database instance, opening it if necessary.
	 * @returns A Promise that resolves with the database instance.
	 */
	public async getDB(): Promise<IDBDatabase> {
		return this.openDB();
	}

	/**
	 * Puts (adds or updates) data into a specific object store.
	 * @param storeName The name of the object store.
	 * @param data The data object to put.
	 */
	public async put<T>(storeName: string, data: T): Promise<void> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readwrite');
			const store = transaction.objectStore(storeName);
			const request = store.put(data);

			request.onsuccess = () => resolve();
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Gets data from a specific object store by key.
	 * @param storeName The name of the object store.
	 * @param key The key of the data to retrieve.
	 * @returns A Promise that resolves with the data object, or null if not found.
	 */
	public async get<T>(storeName: string, key: IDBValidKey): Promise<T | null> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readonly');
			const store = transaction.objectStore(storeName);
			const request = store.get(key);

			request.onsuccess = (event) => {
				resolve((event.target as IDBRequest).result || null);
			};
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Gets all data from a specific object store.
	 * @param storeName The name of the object store.
	 * @returns A Promise that resolves with an array of all data objects.
	 */
	public async getAll<T>(storeName: string): Promise<T[]> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readonly');
			const store = transaction.objectStore(storeName);
			const request = store.getAll();

			request.onsuccess = (event) => {
				resolve((event.target as IDBRequest).result as T[]);
			};
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Deletes data from a specific object store by key.
	 * @param storeName The name of the object store.
	 * @param key The key of the data to delete.
	 */
	public async delete(storeName: string, key: IDBValidKey): Promise<void> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readwrite');
			const store = transaction.objectStore(storeName);
			const request = store.delete(key);

			request.onsuccess = () => resolve();
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Clears all data from a specific object store.
	 * @param storeName The name of the object store.
	 */
	public async clear(storeName: string): Promise<void> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readwrite');
			const store = transaction.objectStore(storeName);
			const request = store.clear();

			request.onsuccess = () => resolve();
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}
}
