import { NextRequest, NextResponse } from 'next/server';
import {
	errorLogger,
	createErrorContext,
	AppError,
	ValidationError,
	UnauthorizedError,
	ForbiddenError,
	NotFoundError,
	ServerError,
	NetworkError,
	TimeoutError,
} from '@/lib/error-handling';

// ============================================================================
// TYPES
// ============================================================================

export interface ApiErrorResponse {
	error: string;
	code?: string;
	details?: any;
	timestamp: string;
	path: string;
	method: string;
}

export type ApiHandler = (
	request: NextRequest,
	context?: { params: any }
) => Promise<NextResponse> | NextResponse;

// ============================================================================
// ERROR MAPPING
// ============================================================================

function mapErrorToHttpStatus(error: Error): number {
	if (error instanceof ValidationError) return 400;
	if (error instanceof UnauthorizedError) return 401;
	if (error instanceof ForbiddenError) return 403;
	if (error instanceof NotFoundError) return 404;
	if (error instanceof TimeoutError) return 408;
	if (error instanceof NetworkError) return 503;
	if (error instanceof ServerError) return 500;
	if (error instanceof AppError) return error.statusCode || 500;

	// Default for unknown errors
	return 500;
}

function createErrorResponse(
	error: Error,
	request: NextRequest,
	includeDetails: boolean = false
): ApiErrorResponse {
	const isAppError = error instanceof AppError;

	return {
		error: isAppError ? error.message : 'Internal server error',
		code: isAppError ? error.code : 'INTERNAL_ERROR',
		details: includeDetails && isAppError ? error.context : undefined,
		timestamp: new Date().toISOString(),
		path: request.nextUrl.pathname,
		method: request.method,
	};
}

// ============================================================================
// ERROR MIDDLEWARE
// ============================================================================

export function withErrorHandling(handler: ApiHandler): ApiHandler {
	return async (request: NextRequest, context?: { params: any }) => {
		const startTime = Date.now();
		const requestId = crypto.randomUUID();

		try {
			// Add request ID to headers for tracing
			const response = await handler(request, context);

			// Log successful requests in development
			if (process.env.NODE_ENV === 'development') {
				const duration = Date.now() - startTime;
				console.log(
					`[${requestId}] ${request.method} ${request.nextUrl.pathname} - ${response.status} (${duration}ms)`
				);
			}

			return response;
		} catch (error) {
			const duration = Date.now() - startTime;
			const normalizedError = error instanceof Error ? error : new Error(String(error));

			// Enhanced error logging
			errorLogger.error(
				`API Error: ${request.method} ${request.nextUrl.pathname}`,
				normalizedError,
				createErrorContext('APIMiddleware', 'request_handler', {
					requestId,
					method: request.method,
					path: request.nextUrl.pathname,
					duration,
					userAgent: request.headers.get('user-agent'),
					ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
				}),
				'APIMiddleware'
			);

			// Determine response details
			const status = mapErrorToHttpStatus(normalizedError);
			const includeDetails = process.env.NODE_ENV === 'development';
			const errorResponse = createErrorResponse(normalizedError, request, includeDetails);

			return NextResponse.json(errorResponse, {
				status,
				headers: {
					'X-Request-ID': requestId,
					'X-Error-Code': errorResponse.code || 'UNKNOWN_ERROR',
				},
			});
		}
	};
}

// ============================================================================
// VALIDATION MIDDLEWARE
// ============================================================================

export function withValidation<T>(
	schema: { parse: (data: unknown) => T },
	handler: (request: NextRequest, data: T, context?: { params: any }) => Promise<NextResponse>
): ApiHandler {
	return withErrorHandling(async (request: NextRequest, context?: { params: any }) => {
		try {
			const body = await request.json();
			const validatedData = schema.parse(body);
			return await handler(request, validatedData, context);
		} catch (error) {
			if (error instanceof Error && error.name === 'ZodError') {
				throw new ValidationError('Invalid request data');
			}
			throw error;
		}
	});
}

// ============================================================================
// RATE LIMITING MIDDLEWARE
// ============================================================================

const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function withRateLimit(
	maxRequests: number = 100,
	windowMs: number = 60000 // 1 minute
) {
	return function (handler: ApiHandler): ApiHandler {
		return withErrorHandling(async (request: NextRequest, context?: { params: any }) => {
			const ip =
				request.headers.get('x-forwarded-for') ||
				request.headers.get('x-real-ip') ||
				'unknown';

			const now = Date.now();
			const key = `${ip}:${request.nextUrl.pathname}`;
			const current = rateLimitMap.get(key);

			if (!current || now > current.resetTime) {
				rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
			} else if (current.count >= maxRequests) {
				throw new AppError('Rate limit exceeded', 'RATE_LIMIT_EXCEEDED', 429);
			} else {
				current.count++;
			}

			return await handler(request, context);
		});
	};
}

// ============================================================================
// AUTHENTICATION MIDDLEWARE
// ============================================================================

export function withAuth(handler: ApiHandler): ApiHandler {
	return withErrorHandling(async (request: NextRequest, context?: { params: any }) => {
		// This would integrate with your auth system
		const authHeader = request.headers.get('authorization');

		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			throw new UnauthorizedError('Missing or invalid authorization header');
		}

		// Add auth validation logic here
		// For now, just pass through
		return await handler(request, context);
	});
}

// ============================================================================
// CORS MIDDLEWARE
// ============================================================================

export function withCors(
	origins: string[] = ['http://localhost:3000'],
	methods: string[] = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
) {
	return function (handler: ApiHandler): ApiHandler {
		return withErrorHandling(async (request: NextRequest, context?: { params: any }) => {
			const origin = request.headers.get('origin');

			// Handle preflight requests
			if (request.method === 'OPTIONS') {
				return new NextResponse(null, {
					status: 200,
					headers: {
						'Access-Control-Allow-Origin':
							origin && origins.includes(origin) ? origin : origins[0],
						'Access-Control-Allow-Methods': methods.join(', '),
						'Access-Control-Allow-Headers': 'Content-Type, Authorization',
						'Access-Control-Max-Age': '86400',
					},
				});
			}

			const response = await handler(request, context);

			// Add CORS headers to response
			if (origin && origins.includes(origin)) {
				response.headers.set('Access-Control-Allow-Origin', origin);
			}
			response.headers.set('Access-Control-Allow-Methods', methods.join(', '));
			response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

			return response;
		});
	};
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export function createApiRoute(handler: ApiHandler) {
	return withErrorHandling(handler);
}

export function createProtectedApiRoute(handler: ApiHandler) {
	return withAuth(withErrorHandling(handler));
}

export function createValidatedApiRoute<T>(
	schema: { parse: (data: unknown) => T },
	handler: (request: NextRequest, data: T, context?: { params: any }) => Promise<NextResponse>
) {
	return withValidation(schema, handler);
}

// ============================================================================
// EXAMPLE USAGE
// ============================================================================

/*
// Basic error handling
export const GET = createApiRoute(async (request) => {
	// Your handler logic
	return NextResponse.json({ data: 'success' });
});

// With authentication
export const POST = createProtectedApiRoute(async (request) => {
	// Your protected handler logic
	return NextResponse.json({ data: 'success' });
});

// With validation
const schema = z.object({
	name: z.string(),
	email: z.string().email(),
});

export const PUT = createValidatedApiRoute(schema, async (request, data) => {
	// data is now typed and validated
	return NextResponse.json({ data });
});

// With multiple middlewares
export const DELETE = withRateLimit(10, 60000)(
	withAuth(
		withErrorHandling(async (request) => {
			// Your handler logic
			return NextResponse.json({ success: true });
		})
	)
);
*/
