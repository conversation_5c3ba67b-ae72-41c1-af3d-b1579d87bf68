'use client';

import { AppError, ErrorSeverity, errorLogger } from './error-handling';

// ============================================================================
// TYPES
// ============================================================================

export interface ErrorReport {
	id: string;
	error: AppError;
	timestamp: string;
	userAgent: string;
	url: string;
	userId?: string;
	sessionId?: string;
	buildVersion?: string;
	environment: string;
	additionalContext?: Record<string, any>;
}

export interface ErrorReportingConfig {
	enabled: boolean;
	endpoint?: string;
	apiKey?: string;
	environment: string;
	buildVersion?: string;
	userId?: string;
	sessionId?: string;
	maxReportsPerSession?: number;
	reportingThreshold?: ErrorSeverity;
	enableLocalStorage?: boolean;
	enableConsoleReporting?: boolean;
	onReportSent?: (report: ErrorReport) => void;
	onReportFailed?: (report: ErrorReport, error: Error) => void;
}

// ============================================================================
// ERROR REPORTING SERVICE
// ============================================================================

export class ErrorReportingService {
	private static instance: ErrorReportingService;
	private config: ErrorReportingConfig;
	private reportCount: number = 0;
	private reportQueue: ErrorReport[] = [];
	private isProcessingQueue: boolean = false;

	private constructor(config: ErrorReportingConfig) {
		this.config = config;
		this.loadReportCount();
		this.setupGlobalErrorHandlers();
	}

	public static getInstance(config?: ErrorReportingConfig): ErrorReportingService {
		if (!ErrorReportingService.instance) {
			if (!config) {
				throw new Error('ErrorReportingService requires initial configuration');
			}
			ErrorReportingService.instance = new ErrorReportingService(config);
		}
		return ErrorReportingService.instance;
	}

	public static configure(config: Partial<ErrorReportingConfig>): void {
		if (ErrorReportingService.instance) {
			ErrorReportingService.instance.updateConfig(config);
		}
	}

	// Update configuration
	public updateConfig(newConfig: Partial<ErrorReportingConfig>): void {
		this.config = { ...this.config, ...newConfig };
	}

	// Report an error
	public async reportError(
		error: AppError | Error,
		additionalContext?: Record<string, any>
	): Promise<void> {
		if (!this.config.enabled) return;

		// Check if we've exceeded the max reports per session
		if (this.config.maxReportsPerSession && this.reportCount >= this.config.maxReportsPerSession) {
			errorLogger.warn(
				'Error reporting limit reached for this session',
				{ reportCount: this.reportCount, maxReports: this.config.maxReportsPerSession },
				'ErrorReporting'
			);
			return;
		}

		// Convert to AppError if needed
		const appError = error instanceof AppError 
			? error 
			: new AppError(error.message, 'UNKNOWN_ERROR');

		// Check severity threshold
		if (this.config.reportingThreshold && 
			this.getSeverityLevel(appError.severity) < this.getSeverityLevel(this.config.reportingThreshold)) {
			return;
		}

		// Create error report
		const report = this.createErrorReport(appError, additionalContext);

		// Add to queue
		this.reportQueue.push(report);
		this.reportCount++;
		this.saveReportCount();

		// Process queue
		await this.processReportQueue();
	}

	// Create error report object
	private createErrorReport(
		error: AppError,
		additionalContext?: Record<string, any>
	): ErrorReport {
		return {
			id: `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			error,
			timestamp: new Date().toISOString(),
			userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
			url: typeof window !== 'undefined' ? window.location.href : '',
			userId: this.config.userId,
			sessionId: this.config.sessionId,
			buildVersion: this.config.buildVersion,
			environment: this.config.environment,
			additionalContext,
		};
	}

	// Process the report queue
	private async processReportQueue(): Promise<void> {
		if (this.isProcessingQueue || this.reportQueue.length === 0) return;

		this.isProcessingQueue = true;

		while (this.reportQueue.length > 0) {
			const report = this.reportQueue.shift()!;
			
			try {
				await this.sendReport(report);
				
				if (this.config.onReportSent) {
					this.config.onReportSent(report);
				}
			} catch (error) {
				errorLogger.error(
					'Failed to send error report',
					error instanceof Error ? error : new Error(String(error)),
					{ reportId: report.id },
					'ErrorReporting'
				);

				if (this.config.onReportFailed) {
					this.config.onReportFailed(report, error instanceof Error ? error : new Error(String(error)));
				}

				// Store failed report locally if enabled
				if (this.config.enableLocalStorage) {
					this.storeReportLocally(report);
				}
			}
		}

		this.isProcessingQueue = false;
	}

	// Send report to external service
	private async sendReport(report: ErrorReport): Promise<void> {
		// Console reporting
		if (this.config.enableConsoleReporting) {
			console.group(`🐛 Error Report: ${report.id}`);
			console.error('Error:', report.error.toLogObject());
			console.info('Context:', {
				url: report.url,
				userAgent: report.userAgent,
				timestamp: report.timestamp,
				additionalContext: report.additionalContext,
			});
			console.groupEnd();
		}

		// External service reporting
		if (this.config.endpoint) {
			const response = await fetch(this.config.endpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
				},
				body: JSON.stringify(this.sanitizeReport(report)),
			});

			if (!response.ok) {
				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			}
		}
	}

	// Sanitize report for external transmission
	private sanitizeReport(report: ErrorReport): any {
		return {
			id: report.id,
			message: report.error.message,
			code: report.error.code,
			severity: report.error.severity,
			category: report.error.category,
			statusCode: report.error.statusCode,
			timestamp: report.timestamp,
			userAgent: report.userAgent,
			url: report.url,
			userId: report.userId,
			sessionId: report.sessionId,
			buildVersion: report.buildVersion,
			environment: report.environment,
			context: report.error.context,
			additionalContext: report.additionalContext,
		};
	}

	// Store report locally for later transmission
	private storeReportLocally(report: ErrorReport): void {
		if (typeof localStorage === 'undefined') return;

		try {
			const stored = localStorage.getItem('error_reports') || '[]';
			const reports = JSON.parse(stored);
			reports.push(this.sanitizeReport(report));
			
			// Keep only the last 50 reports
			if (reports.length > 50) {
				reports.splice(0, reports.length - 50);
			}
			
			localStorage.setItem('error_reports', JSON.stringify(reports));
		} catch (error) {
			errorLogger.error(
				'Failed to store error report locally',
				error instanceof Error ? error : new Error(String(error)),
				{ reportId: report.id },
				'ErrorReporting'
			);
		}
	}

	// Load and send stored reports
	public async sendStoredReports(): Promise<void> {
		if (typeof localStorage === 'undefined') return;

		try {
			const stored = localStorage.getItem('error_reports');
			if (!stored) return;

			const reports = JSON.parse(stored);
			localStorage.removeItem('error_reports');

			for (const reportData of reports) {
				try {
					await this.sendStoredReport(reportData);
				} catch (error) {
					errorLogger.error(
						'Failed to send stored error report',
						error instanceof Error ? error : new Error(String(error)),
						{ reportId: reportData.id },
						'ErrorReporting'
					);
				}
			}
		} catch (error) {
			errorLogger.error(
				'Failed to process stored error reports',
				error instanceof Error ? error : new Error(String(error)),
				{},
				'ErrorReporting'
			);
		}
	}

	// Send a stored report
	private async sendStoredReport(reportData: any): Promise<void> {
		if (!this.config.endpoint) return;

		const response = await fetch(this.config.endpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
			},
			body: JSON.stringify(reportData),
		});

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}
	}

	// Setup global error handlers
	private setupGlobalErrorHandlers(): void {
		if (typeof window === 'undefined') return;

		// Unhandled JavaScript errors
		window.addEventListener('error', (event) => {
			const error = new AppError(
				event.message || 'Unhandled JavaScript error',
				'UNHANDLED_ERROR',
				500,
				ErrorSeverity.HIGH
			);

			this.reportError(error, {
				filename: event.filename,
				lineno: event.lineno,
				colno: event.colno,
				stack: event.error?.stack,
			});
		});

		// Unhandled promise rejections
		window.addEventListener('unhandledrejection', (event) => {
			const error = new AppError(
				event.reason?.message || 'Unhandled promise rejection',
				'UNHANDLED_REJECTION',
				500,
				ErrorSeverity.HIGH
			);

			this.reportError(error, {
				reason: event.reason,
				stack: event.reason?.stack,
			});
		});
	}

	// Get severity level as number for comparison
	private getSeverityLevel(severity: ErrorSeverity): number {
		switch (severity) {
			case ErrorSeverity.LOW: return 1;
			case ErrorSeverity.MEDIUM: return 2;
			case ErrorSeverity.HIGH: return 3;
			case ErrorSeverity.CRITICAL: return 4;
			default: return 0;
		}
	}

	// Load report count from storage
	private loadReportCount(): void {
		if (typeof sessionStorage === 'undefined') return;

		try {
			const stored = sessionStorage.getItem('error_report_count');
			this.reportCount = stored ? parseInt(stored, 10) : 0;
		} catch {
			this.reportCount = 0;
		}
	}

	// Save report count to storage
	private saveReportCount(): void {
		if (typeof sessionStorage === 'undefined') return;

		try {
			sessionStorage.setItem('error_report_count', this.reportCount.toString());
		} catch {
			// Ignore storage errors
		}
	}

	// Get current statistics
	public getStats(): { reportCount: number; queueLength: number } {
		return {
			reportCount: this.reportCount,
			queueLength: this.reportQueue.length,
		};
	}
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

export function initializeErrorReporting(config: ErrorReportingConfig): ErrorReportingService {
	return ErrorReportingService.getInstance(config);
}

export function reportError(
	error: AppError | Error,
	additionalContext?: Record<string, any>
): Promise<void> {
	const service = ErrorReportingService.getInstance();
	return service.reportError(error, additionalContext);
}

export function configureErrorReporting(config: Partial<ErrorReportingConfig>): void {
	ErrorReportingService.configure(config);
}
