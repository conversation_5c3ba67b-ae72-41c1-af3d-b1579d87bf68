'use server';

import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getUserService } from '@/backend/wire'; // Assumed: Service getter
import { auth } from '@/lib';
import { UserWithDetail } from '@/models/user'; // Assuming this is the desired return type for user fetches
import { Provider } from '@prisma/client';

/**
 * Creates a new user.
 * This API does not require authentication for the *caller*, as it's typically used for new user registration flows.
 * @param data - Object containing `provider`, `providerId`, and optional `email`/`name`.
 * @returns A promise that resolves to an object with the new user's ID: `{ id: string }`.
 * @throws {ValidationError} If `provider` or `providerId` is missing.
 * @throws {Error} If user creation fails (e.g., duplicate providerId if service throws, database error).
 */
export async function createUserApi(data: {
	provider: Provider;
	providerId: string;
	email?: string;
	name?: string;
}): Promise<{ id: string }> {
	if (!data || typeof data !== 'object') {
		throw new ValidationError('Data object is required to create a user.');
	}
	const { provider, providerId, email, name } = data;

	if (!provider) {
		throw new ValidationError('Provider is required to create a user.');
	}
	if (!providerId) {
		throw new ValidationError('Provider ID is required to create a user.');
	}
	if (!Object.values(Provider).includes(provider)) {
		throw new ValidationError(`Invalid provider: ${provider}`);
	}

	const userService = getUserService();
	try {
		// Original logic: CreateUserCommand(data.provider, data.providerId, data.email, data.name)
		// Handler called: userService.createUser({ provider, provider_id: providerId, email, name })
		const userObject = await userService.createUser({
			provider: provider,
			provider_id: providerId, // Service expects provider_id
			email: email,
			name: name,
		});
		// The command handler returned { id: user.id }
		return { id: userObject.id };
	} catch (error) {
		if (error instanceof ValidationError) {
			// For example, if service validates and finds a duplicate
			throw error;
		}
		console.error(
			`Failed to create user with provider ${provider}, providerId ${providerId}:`,
			error
		);
		throw new Error('Failed to create user. Please try again.');
	}
}

/**
 * Retrieves a user by their provider and provider ID.
 * This API might be used internally or for specific authorized scenarios.
 * It does not perform `auth()` for the caller by default; access control is assumed to be handled
 * by the caller or within the service if this endpoint is exposed broadly.
 * @param provider - The authentication provider (e.g., `Provider.TELEGRAM`).
 * @param providerId - The user's unique identifier from the specified provider.
 * @returns A promise that resolves to the `UserWithDetail` object or `null` if not found.
 * @throws {ValidationError} If `provider` or `providerId` is missing.
 * @throws {Error} If fetching the user fails for other reasons.
 */
export async function getUserByProviderApi(
	provider: Provider,
	providerId: string
): Promise<UserWithDetail | null> {
	if (!provider) {
		throw new ValidationError('Provider is required to fetch user by provider details.');
	}
	if (!providerId) {
		throw new ValidationError('Provider ID is required to fetch user by provider details.');
	}
	if (!Object.values(Provider).includes(provider)) {
		throw new ValidationError(`Invalid provider: ${provider}`);
	}

	const userService = getUserService();
	try {
		// Original logic: GetUserByProviderQuery(provider, providerId)
		// Handler called: userService.getUserByProviderId(query.provider, query.providerId)
		// Note: service method name is getUserByProviderId
		const user = await userService.getUserByProviderId(provider, providerId);
		// Service returns Prisma User | null. API expects UserWithDetail | null.
		// Casting implies UserWithDetail is structurally compatible or service returns it.
		return user as UserWithDetail | null;
	} catch (error) {
		if (error instanceof ValidationError) {
			throw error;
		}
		console.error(
			`Failed to get user by provider ${provider}, providerId ${providerId}:`,
			error
		);
		throw new Error('Failed to retrieve user by provider. Please try again.');
	}
}

/**
 * Retrieves the currently authenticated user's details.
 * @returns A promise that resolves to the `UserWithDetail` object, or `null` if the user is not found by ID (though auth check should prevent this).
 * @throws {UnauthorizedError} If the user is not authenticated.
 * @throws {Error} If fetching the user's details fails for other reasons.
 */
export async function getCurrentUserApi(): Promise<UserWithDetail | null> {
	const session = await auth();
	const userId = session?.user?.id;
	if (!userId) {
		throw new UnauthorizedError(
			'Unauthorized: No authenticated user session found to fetch current user.'
		);
	}

	const userService = getUserService();
	try {
		// Original logic: GetUserByIdQuery(session.user.id)
		// Handler called: userService.getUserById(query.userId)
		const user = await userService.getUserById(userId);
		return user as UserWithDetail | null; // Cast Prisma User to UserWithDetail
	} catch (error) {
		if (error instanceof UnauthorizedError) {
			// Should not happen if userId is present, but good practice
			throw error;
		}
		console.error(`Failed to get current user (ID: ${userId}):`, error);
		throw new Error('Failed to retrieve current user information. Please try again.');
	}
}

/**
 * Retrieves a specific user by their internal ID.
 * This API is for fetching any user by ID and should be protected by appropriate authorization
 * checks by the caller or within the service if exposed broadly. It does not perform `auth()` for the *calling* user.
 * @param userIdToFetch - The internal ID of the user to retrieve.
 * @returns A promise that resolves to the `UserWithDetail` object or `null` if not found.
 * @throws {ValidationError} If `userIdToFetch` is missing.
 * @throws {Error} If fetching the user fails for other reasons.
 */
export async function getUserByIdApi(userIdToFetch: string): Promise<UserWithDetail | null> {
	if (!userIdToFetch) {
		throw new ValidationError('User ID to fetch is required.');
	}

	const userService = getUserService();
	try {
		// Original logic: GetUserByIdQuery(userIdToFetch)
		// Handler called: userService.getUserById(query.userId)
		const user = await userService.getUserById(userIdToFetch);
		return user as UserWithDetail | null; // Cast Prisma User to UserWithDetail
	} catch (error) {
		// If userService.getUserById could throw NotFoundError for the userIdToFetch, it could be rethrown.
		// e.g., if (error instanceof NotFoundError && error.message.includes(userIdToFetch)) throw error;
		console.error(`Failed to get user by ID ${userIdToFetch}:`, error);
		throw new Error('Failed to fetch user by ID. Please try again.');
	}
}
