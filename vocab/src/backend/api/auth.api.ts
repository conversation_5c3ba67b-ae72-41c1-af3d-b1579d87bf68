'use server';

import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { generateToken } from '@/backend/utils/token.util';
import { getAuthService } from '@/backend/wire';
import { getAuthConfig, getServerConfig } from '@/config';
import { Provider } from '@prisma/client';
import { cookies } from 'next/headers';

export async function logoutApi(): Promise<void> {
	const cookieStore = await cookies();
	const authConfig = await getAuthConfig();
	cookieStore.delete(authConfig.jwtCookieName);
}

export async function providerLoginApi(provider: Provider, providerId: string): Promise<void> {
	if (!provider || !providerId || !Object.values(Provider).includes(provider)) {
		throw new ValidationError('Invalid provider or provider ID');
	}

	const authService = getAuthService();
	const user = await authService.providerLogin(provider, providerId);
	if (!user) throw new UnauthorizedError('Authentication failed');

	const token = await generateToken(user);
	if (!token) throw new Error('Token generation failed');

	const cookieStore = await cookies();
	const authConfig = await getAuthConfig();
	const serverConfig = await getServerConfig();
	cookieStore.set({
		name: authConfig.jwtCookieName,
		value: token,
		httpOnly: true,
		secure: serverConfig.env === 'production',
		sameSite: 'strict',
		path: '/',
		maxAge: authConfig.jwtExpiresIn,
	});
}

/**
 * Đăng nhập bằng username và password
 * Tự động tạo tài khoản mới nếu username chưa tồn tại
 * @param username - Tên đăng nhập
 * @param password - Mật khẩu
 * @throws {ValidationError} Nếu thiếu username hoặc password
 * @throws {UnauthorizedError} Nếu mật khẩu không đúng
 */
export async function usernamePasswordLoginApi(username: string, password: string): Promise<void> {
	if (!username || !password) {
		throw new ValidationError('Username and password are required');
	}

	if (username.trim().length < 3) {
		throw new ValidationError('Username must be at least 3 characters long');
	}

	if (password.length < 6) {
		throw new ValidationError('Password must be at least 6 characters long');
	}

	try {
		const authService = getAuthService();
		const user = await authService.usernamePasswordLogin(username.trim(), password);

		const token = await generateToken(user);
		if (!token) throw new Error('Token generation failed');

		const cookieStore = await cookies();
		const authConfig = await getAuthConfig();
		const serverConfig = await getServerConfig();
		cookieStore.set({
			name: authConfig.jwtCookieName,
			value: token,
			httpOnly: true,
			secure: serverConfig.env === 'production',
			sameSite: 'strict',
			path: '/',
			maxAge: authConfig.jwtExpiresIn,
		});
	} catch (error) {
		if (error instanceof Error && error.message === 'Invalid password') {
			throw new UnauthorizedError('Invalid username or password');
		}
		throw error;
	}
}
