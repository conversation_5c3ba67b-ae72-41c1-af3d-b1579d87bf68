import { Prisma, Provider, User, PrismaClient } from '@prisma/client';
import { BaseRepository, BaseRepositoryImpl } from './base.repository';

export interface UserRepository extends BaseRepository<User> {
	findByProviderId(provider: Provider, providerId: string): Promise<User | null>;
	findByUsername(username: string): Promise<User | null>;
	searchUsers(searchTerm: string, limit?: number): Promise<User[]>;
}

export class UserRepositoryImpl extends BaseRepositoryImpl<User> implements UserRepository {
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.user);
	}

	async findByProviderId(provider: Provider, providerId: string): Promise<User | null> {
		const user = await this.prisma.user.findUnique({
			where: {
				provider_provider_id: {
					provider,
					provider_id: providerId,
				},
			},
		});
		return user;
	}

	async findByUsername(username: string): Promise<User | null> {
		const user = await this.prisma.user.findUnique({
			where: {
				username,
			},
		});
		return user;
	}

	async searchUsers(searchTerm: string, limit: number = 10): Promise<User[]> {
		const users = await this.prisma.user.findMany({
			where: {
				OR: [{ provider_id: { contains: searchTerm, mode: 'insensitive' } }],
			},
			take: limit,
		});
		return users;
	}

	override async findById(id: string): Promise<User | null> {
		const user = await this.prisma.user.findUnique({
			where: { id },
		});
		return user;
	}

	override async findOne(query: Prisma.UserWhereInput): Promise<User | null> {
		const user = await this.prisma.user.findFirst({
			where: query,
		});
		return user;
	}

	override async find(query: Prisma.UserWhereInput): Promise<User[]> {
		const users = await this.prisma.user.findMany({
			where: query,
		});
		return users;
	}

	override async create(data: Prisma.UserCreateInput): Promise<User> {
		const user = await this.prisma.user.create({
			data,
		});
		return user;
	}

	override async update(id: string, data: Prisma.UserUpdateInput): Promise<User> {
		const user = await this.prisma.user.update({
			where: { id },
			data,
		});
		return user;
	}
}
