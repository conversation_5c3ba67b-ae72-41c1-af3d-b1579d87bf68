import { LastSeenWord, PrismaClient } from '@prisma/client';
import { BaseRepository, BaseRepositoryImpl } from '@/backend/repositories';

export interface LastSeenWordRepository extends BaseRepository<LastSeenWord> {
	findLastSeenWord(userId: string, wordId: string): Promise<LastSeenWord | null>;
	findLastSeenWordsByWordIds(userId: string, wordIds: string[]): Promise<LastSeenWord[]>;
	updateOrCreateLastSeenWord(userId: string, wordId: string): Promise<LastSeenWord>;
}

export class LastSeenWordRepositoryImpl
	extends BaseRepositoryImpl<LastSeenWord>
	implements LastSeenWordRepository
{
	constructor(private readonly prisma: PrismaClient) {
		super(prisma.lastSeenWord);
	}

	async findLastSeenWord(userId: string, wordId: string): Promise<LastSeenWord | null> {
		return this.prisma.lastSeenWord.findUnique({
			where: {
				user_id_word_id: {
					user_id: userId,
					word_id: wordId,
				},
			},
		});
	}

	async findLastSeenWordsByWordIds(userId: string, wordIds: string[]): Promise<LastSeenWord[]> {
		return this.prisma.lastSeenWord.findMany({
			where: {
				user_id: userId,
				word_id: {
					in: wordIds,
				},
			},
		});
	}

	async updateOrCreateLastSeenWord(userId: string, wordId: string): Promise<LastSeenWord> {
		return this.prisma.lastSeenWord.upsert({
			where: {
				user_id_word_id: {
					user_id: userId,
					word_id: wordId,
				},
			},
			update: {
				last_seen_at: new Date(),
				review_count: {
					increment: 1,
				},
			},
			create: {
				user_id: userId,
				word_id: wordId,
				last_seen_at: new Date(),
			},
		});
	}
}
