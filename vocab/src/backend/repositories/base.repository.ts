import { errorLogger, createErro<PERSON><PERSON>ontext, ServerError } from '@/lib/error-handling';

export type PrismaModel<T> = {
	findUnique(args: { where: { id: string } }): Promise<T | null>;
	findFirst(args: { where: Record<string, unknown> }): Promise<T | null>;
	findMany(args: { where: Record<string, unknown> }): Promise<T[]>;
	create(args: { data: Record<string, unknown> }): Promise<T>;
	update(args: { where: { id: string }; data: Record<string, unknown> }): Promise<T>;
	deleteMany(args: { where: Record<string, unknown> }): Promise<{ count: number }>;
};

export interface BaseRepository<T extends { id: string }> {
	findById(id: string): Promise<T | null>;
	findOne(query: Record<string, unknown>): Promise<T | null>;
	find(query: Record<string, unknown>): Promise<T[]>;
	create(data: Record<string, unknown>): Promise<T>;
	update(id: string, data: Record<string, unknown>): Promise<T>;
	delete(query: Record<string, unknown>): Promise<void>;
}

export class BaseRepositoryImpl<T extends { id: string }> implements BaseRepository<T> {
	constructor(protected readonly model: PrismaModel<T>) {}

	async findById(id: string): Promise<T | null> {
		try {
			return await this.model.findUnique({ where: { id } });
		} catch (error) {
			errorLogger.error(
				'Database query failed in findById',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseRepository', 'find_by_id', { id }),
				'BaseRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	findOne(query: Record<string, unknown>): Promise<T | null> {
		return this.model.findFirst({ where: query });
	}

	find(query: Record<string, unknown>): Promise<T[]> {
		return this.model.findMany({ where: query });
	}

	create(data: Record<string, unknown>): Promise<T> {
		return this.model.create({ data });
	}

	update(id: string, data: Record<string, unknown>): Promise<T> {
		return this.model.update({ where: { id }, data });
	}

	async delete(query: Record<string, unknown>): Promise<void> {
		await this.model.deleteMany({ where: query });
	}
}
