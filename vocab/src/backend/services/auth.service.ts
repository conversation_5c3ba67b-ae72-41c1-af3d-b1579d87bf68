import { Provider, User } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { UserService } from './user.service';

export interface AuthService {
	providerLogin(provider: Provider, provider_id: string): Promise<User>;
	usernamePasswordLogin(username: string, password: string): Promise<User>;
	getUserById(userId: string): Promise<User | null>;
	findOrCreateUserByProvider(provider: Provider, providerId: string): Promise<User>;
}

export class AuthServiceImpl implements AuthService {
	constructor(private readonly getUserService: () => UserService) {}

	async providerLogin(provider: Provider, provider_id: string): Promise<User> {
		let user = await this.getUserService().getUserByProviderId(provider, provider_id);
		if (!user) {
			user = await this.getUserService().createUser({
				provider,
				provider_id,
			});
		}

		return user;
	}

	async usernamePasswordLogin(username: string, password: string): Promise<User> {
		let user = await this.getUserService().getUserByUsername(username);
		
		if (!user) {
			// Tạo user mới nếu chưa tồn tại
			const hashedPassword = await bcrypt.hash(password, 10);
			user = await this.getUserService().createUserWithPassword({
				username,
				password_hash: hashedPassword,
				provider: Provider.USERNAME_PASSWORD,
				provider_id: username, // Sử dụng username làm provider_id
			});
		} else {
			// Kiểm tra mật khẩu nếu user đã tồn tại
			if (!user.password_hash) {
				throw new Error('User exists but has no password set');
			}
			
			const isValidPassword = await bcrypt.compare(password, user.password_hash);
			if (!isValidPassword) {
				throw new Error('Invalid password');
			}
		}

		return user;
	}

	async getUserById(userId: string): Promise<User | null> {
		return this.getUserService().getUserById(userId);
	}

	async findOrCreateUserByProvider(provider: Provider, providerId: string): Promise<User> {
		let user = await this.getUserService().getUserByProviderId(provider, providerId);

		if (!user) {
			user = await this.getUserService().createUser({
				provider,
				provider_id: providerId,
			});
		}

		return user;
	}
}
