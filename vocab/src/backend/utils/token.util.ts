'use server';

import { User } from '@prisma/client';
import { getAuthConfig } from '@/config';
import { SignJWT, jwtVerify, type JWTPayload } from 'jose';

/**
 * Generates a JWT token for user authentication
 * @param user The user object to generate the token for
 * @returns A JWT token string
 */
export async function generateToken(user: User): Promise<string> {
	const authConfig = await getAuthConfig();

	const iat = Math.floor(Date.now() / 1000);
	const exp = iat + authConfig.jwtExpiresIn;
	const sub = user.id;
	const jwt = new SignJWT({ sub })
		.setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
		.setExpirationTime(exp)
		.setIssuedAt(iat)
		.setNotBefore(iat)
		.sign(new TextEncoder().encode(authConfig.jwtSecret));
	return jwt;
}

/**
 * Verifies a JWT token and returns the decoded payload
 * @param token The JWT token to verify
 * @returns The decoded token payload
 */
export async function verifyToken(token: string): Promise<JWTPayload> {
	try {
		const authConfig = await getAuthConfig();
		const { payload } = await jwtVerify<JWTPayload>(
			token,
			new TextEncoder().encode(authConfig.jwtSecret)
		);
		return payload;
	} catch (error) {
		throw new Error('Invalid token');
	}
}
