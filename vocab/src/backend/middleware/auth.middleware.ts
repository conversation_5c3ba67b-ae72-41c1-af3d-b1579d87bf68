import { jwtVerify, type J<PERSON><PERSON>ayload } from 'jose';
import type { NextRequest } from 'next/server';

/**
 * Verifies a JWT token in middleware context
 */
async function verifyTokenInMiddleware(token: string, secret: string): Promise<JWTPayload> {
	try {
		const { payload } = await jwtVerify<JWTPayload>(token, new TextEncoder().encode(secret));
		return payload;
	} catch {
		throw new Error('Invalid token');
	}
}

export async function authMiddleware(request: NextRequest) {
	const jwtCookieName = process.env.JWT_COOKIE_NAME;
	const jwtSecret = process.env.JWT_SECRET;

	if (!jwtCookieName?.length || !jwtSecret?.length) {
		console.error('JWT_COOKIE_NAME or JWT_SECRET is not defined in environment variables');
		return Response.json({ error: 'Internal server error' }, { status: 500 });
	}

	// In production, extract token from cookie
	const token = request.cookies.get(jwtCookieName)?.value;

	if (!token) {
		return Response.json({ error: 'Unauthorized - No token provided' }, { status: 401 });
	}

	// Get JWT secret from environment
	if (!jwtSecret) {
		console.error('JWT secret is not defined in environment variables');
		return Response.json({ error: 'Internal server error' }, { status: 500 });
	}

	try {
		// Verify and decode token
		const payload = await verifyTokenInMiddleware(token, jwtSecret);
		const userId = payload.sub;

		if (!userId) {
			return Response.json(
				{ error: 'Unauthorized - Invalid user ID in token' },
				{ status: 401 }
			);
		}

		request.headers.set('x-user-id', userId);

		return request;
	} catch (error) {
		console.error('Token verification failed:', error);
		return Response.json({ error: 'Unauthorized - Invalid token' }, { status: 401 });
	}
}
