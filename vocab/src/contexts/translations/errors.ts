import { TranslationDict } from './translation-dict';

export const errorsTranslations: TranslationDict = {
	'errors.general_fetch_error': {
		EN: 'Failed to fetch data',
		VI: 'Tải dữ liệu thất bại'
	},
	'qa_practice.errors.generation_failed_title': {
		EN: 'Generation Failed',
		VI: 'Tạo thất bại'
	},	'errors.404_title': {
		EN: 'Oops! Page not found',
		VI: 'Oops! Trang không tìm thấy'
	},
	'errors.404_description': {
		EN: "It looks like you've gotten lost on your vocabulary learning journey. Don't worry, we'll get you back on track!",
		VI: 'C<PERSON> vẻ như bạn đã lạc đường trong hành trình học từ vựng. Đừng lo, chúng ta sẽ đưa bạn trở về đúng hướng!'
	},
	'errors.404_home_button': {
		EN: 'Go to homepage',
		VI: 'Về trang chủ'
	},
	'errors.404_collections_button': {
		EN: 'View collections',
		VI: '<PERSON>em bộ sưu tập'
	},
	'errors.404_fun_fact_prefix': {
		EN: 'Did you know?',
		VI: 'Bạn có biết?'
	},
	'errors.404_fun_fact': {
		EN: 'The term "404" comes from HTTP error codes, but in the world of vocabulary learning, it\'s just an opportunity to discover more!',
		VI: 'Từ "404" xuất phát từ mã lỗi HTTP, nhưng trong thế giới học từ vựng, đây chỉ là cơ hội để khám phá thêm!'
	},
	'errors.collections_load_error': {
		EN: 'An error occurred. You may need to log in to view collections.',
		VI: 'Có lỗi xảy ra. Bạn có thể cần đăng nhập để xem bộ sưu tập.'
	}
};
