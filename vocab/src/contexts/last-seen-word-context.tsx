'use client';

import { getWordsToReviewApi, saveLastSeenWordApi } from '@/backend/api';
import { LOADING_SCOPES } from '@/constants';
import { WordDetail } from '@/models';
import { createContext, useCallback, useContext, useMemo, useState } from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';

type LastSeenWordContextType = {
	isLoading: boolean;
	error: Error | null;
	saveWord: (wordId: string) => Promise<void>;
	getWords: (collectionId: string, limit?: number, offset?: number) => Promise<WordDetail[]>;
	getLoadingState: (key: string) => boolean;
};

const LastSeenWordContext = createContext<LastSeenWordContextType | undefined>(undefined);

export function LastSeenWordProvider({ children }: { children: React.ReactNode }) {
	const [error, setError] = useState<Error | null>(null);

	const { getLoading } = useScopedLoading(LOADING_SCOPES.LAST_SEEN_WORD);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.LAST_SEEN_WORD);

	// Computed loading state from any last seen word operation
	const isLoading = getLoading('saveWord') || getLoading('getWords');

	const saveWord = useCallback(
		async (wordId: string) => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'saveWord');
			start();

			try {
				await saveLastSeenWordApi(wordId);
				end();
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to save last seen word');
				end(error);
			}
		},
		[loadingErrorHelper]
	);

	const getWords = useCallback(
		async (collectionId: string, limit?: number, offset?: number): Promise<WordDetail[]> => {
			const { start, end } = loadingErrorHelper(() => {}, setError, 'getWords');
			start();

			try {
				const result = await getWordsToReviewApi(collectionId, limit);
				end();
				return result;
			} catch (err) {
				const error =
					err instanceof Error ? err : new Error('Failed to get words to review');
				end(error);
				return [];
			}
		},
		[loadingErrorHelper]
	);

	const value = useMemo(
		() => ({
			saveWord,
			getWords,
			isLoading,
			error,
			getLoadingState: getLoading,
		}),
		[saveWord, getWords, isLoading, error, getLoading]
	);

	return <LastSeenWordContext.Provider value={value}>{children}</LastSeenWordContext.Provider>;
}

export function useLastSeenWordContext() {
	const context = useContext(LastSeenWordContext);
	if (context === undefined) {
		throw new Error('useLastSeenWordContext must be used within a LastSeenWordProvider');
	}
	return context;
}

// Export hook for backward compatibility
export function useLastSeenWord() {
	return useLastSeenWordContext();
}
