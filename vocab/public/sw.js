const CACHE_NAME = 'vocab-cache-v1';
const STATIC_CACHE_NAME = 'vocab-static-cache-v1';
const DYNAMIC_CACHE_NAME = 'vocab-dynamic-cache-v1';

// Static assets to cache
const urlsToCache = [
	'/',
	'/index.html',
	'/manifest.json',
	'/favicon.ico',
	// '/globe.svg',
	// '/next.svg',
	// '/file.svg',
	// '/window.svg',
	// Add other static assets
];

// Cache strategies
const CACHE_STRATEGIES = {
	STATIC: 'static',
	DYNAMIC: 'dynamic',
	NETWORK_FIRST: 'network-first',
	CACHE_FIRST: 'cache-first',
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
	event.waitUntil(
		Promise.all([
			caches.open(STATIC_CACHE_NAME).then((cache) => {
				console.log('Opened static cache');
				return cache.addAll(urlsToCache);
			}),
			caches.open(DYNAMIC_CACHE_NAME).then((cache) => {
				console.log('Opened dynamic cache');
				return cache;
			}),
		])
	);
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
	event.waitUntil(
		caches.keys().then((cacheNames) => {
			return Promise.all(
				cacheNames.map((cacheName) => {
					if (![STATIC_CACHE_NAME, DYNAMIC_CACHE_NAME].includes(cacheName)) {
						return caches.delete(cacheName);
					}
				})
			);
		})
	);
});

// Helper function to determine cache strategy
const getCacheStrategy = (request) => {
	const url = new URL(request.url);

	// Static assets
	if (
		url.pathname.endsWith('.js') ||
		url.pathname.endsWith('.css') ||
		url.pathname.endsWith('.svg') ||
		url.pathname.endsWith('.png') ||
		url.pathname.endsWith('.jpg') ||
		url.pathname.endsWith('.ico')
	) {
		return CACHE_STRATEGIES.STATIC;
	}

	// API requests
	if (url.pathname.startsWith('/api/')) {
		return CACHE_STRATEGIES.NETWORK_FIRST;
	}

	// Default to dynamic for other requests
	return CACHE_STRATEGIES.DYNAMIC;
};

// Fetch event - handle different caching strategies
self.addEventListener('fetch', (event) => {
	const request = event.request;
	const strategy = getCacheStrategy(request);

	// Skip non-GET requests
	if (request.method !== 'GET') {
		return;
	}

	// Handle different caching strategies
	switch (strategy) {
		case CACHE_STRATEGIES.STATIC:
			event.respondWith(
				caches.match(request).then((response) => {
					return response || fetchAndCache(request, STATIC_CACHE_NAME);
				})
			);
			break;

		case CACHE_STRATEGIES.NETWORK_FIRST:
			event.respondWith(
				fetch(request)
					.then((response) => {
						// Cache the response
						const responseClone = response.clone();
						caches.open(DYNAMIC_CACHE_NAME).then((cache) => {
							cache.put(request, responseClone);
						});
						return response;
					})
					.catch(() => {
						return caches.match(request);
					})
			);
			break;

		case CACHE_STRATEGIES.DYNAMIC:
			event.respondWith(
				caches.match(request).then((response) => {
					return response || fetchAndCache(request, DYNAMIC_CACHE_NAME);
				})
			);
			break;
	}
});

// Helper function to fetch and cache responses
const fetchAndCache = async (request, cacheName) => {
	try {
		const response = await fetch(request);

		// Only cache successful responses
		if (response && response.status === 200) {
			const responseClone = response.clone();
			const cache = await caches.open(cacheName);
			cache.put(request, responseClone);
		}

		return response;
	} catch (error) {
		console.error('Fetch failed:', error);
		throw error;
	}
};
